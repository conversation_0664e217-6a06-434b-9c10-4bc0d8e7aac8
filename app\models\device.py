from sqlalchemy import Column, Integer, String, DateTime, <PERSON><PERSON><PERSON>, ForeignKey, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from app.database import Base

class Device(Base):
    __tablename__ = "devices"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False, unique=True, index=True)
    ip_address = Column(String(50), nullable=False, unique=True, index=True)
    device_type = Column(String(50), nullable=False)
    manufacturer = Column(String(100), nullable=True)
    model = Column(String(100), nullable=True)
    username = Column(String(100), nullable=True)
    password = Column(String(255), nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, nullable=True)
    protocol = Column(String(100), nullable=True)
    port = Column(Integer, nullable=True)
    netmiko_device_type = Column(String(100), nullable=True)
    last_check_time = Column(String(100), nullable=True)
    status = Column(String(50), nullable=True)
    
    # 添加CPU、内存和版本信息字段
    cpu_usage = Column(String(50), nullable=True)
    memory_usage = Column(String(50), nullable=True)
    version_info = Column(String(255), nullable=True)
    
    # 添加机房、机柜和U位字段
    datacenter = Column(String(255), nullable=True)
    rack = Column(String(100), nullable=True)
    position = Column(Integer, nullable=True)
    
    # 添加序列号字段
    serial_number = Column(String(255), nullable=True, unique=True, index=True)

    # 添加MAC地址相关字段
    primary_mac = Column(String(50), nullable=True)  # 主IP的MAC地址
    additional_macs = Column(Text, nullable=True)    # 其他IP的MAC地址信息(JSON格式)
    mac_status = Column(String(20), default='pending')  # pending, collected, failed
    mac_last_updated = Column(DateTime, nullable=True)  # MAC地址最后更新时间

    # 关系：一个设备可以有多个配置
    configurations = relationship("Configuration", back_populates="device", cascade="all, delete-orphan")

    
    # 在Device类中添加
    inspection_results = relationship("InspectionResult", back_populates="device")
    
    class Config:
        from_attributes = True  # 使用Pydantic v2的新配置名