from fastapi import FastAPI, Depends, Query, APIRouter, Request, Header, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import os
import logging
import datetime
import platform
import fastapi
from sqlalchemy.orm import Session
from fastapi.responses import FileResponse, JSONResponse, HTMLResponse
import uvicorn
from fastapi.routing import APIRoute
import traceback  # 添加traceback模块导入
from starlette.responses import RedirectResponse
from typing import List, Optional, Dict, Any

from app import models
from app.database import engine, get_db, check_database_connection
# 导入IP冲突检测模型以确保表被创建
from app.models import ip_conflict_detection
from app.routers.device import router as device_router
from app.routers.config_router import router as config_router
from app.routers.configuration import router as configuration_router
from app.routers.statistics import router as statistics_router
from app.routers.config_generator import router as config_generator_router  # Import the config_generator router
from app.routers.rack_management import router as rack_management_router  # 导入机柜管理路由
from app.models import Base  # 只导入Base
from .webssh.router import router as webssh_router  # 新增的WebSSH路由
from .routers import router
from app.routers import ip_management  # 确保这行存在
from app.routers import port_monitoring
from app.routers.inspection_router import router as inspection_router  # Import inspection router
from app.routers.system import router as system_router  # 导入新的系统信息路由
from app.routers.ai_assistant import router as ai_assistant_router  # 导入AI助手路由
from app.routers.webssh_ai import router as webssh_ai_router  # 导入WebSSH AI助手路由
from app.routers.template_router import router as template_router  # 导入模板管理路由
from app.routers.device_ports import router as device_ports_router  # 导入设备端口路由
from app.routers.license_router import router as license_router  # 导入授权管理路由
from app.routers.batch_ping import router as batch_ping_router  # 导入批量ping路由
from app.routers.new.vlan_router import router as vlan_router
from app.routers.datacenter import router as datacenter_router  # 导入机房管理路由
from app.test_routes import router as test_routes_router  # 导入测试路由
from sqlalchemy.types import Boolean

from app.routers import device, ip_management, batch_ping, ai_assistant, ai_operations
from app.routers.ai_operations import router as ai_operations_router  # 导入AI运维路由
from app.routers.network_tools import router as network_tools_router  # 导入网络工具路由
from app.routers.ip_conflict_detection import router as ip_conflict_router  # 导入IP冲突检测路由
from app.routers.device_ip_integration_simple import router as device_ip_integration_router  # 导入设备IP集成路由
from app.routers import config_monitoring  # 导入配置监控路由

# 确保日志目录存在
os.makedirs('log', exist_ok=True)

# 设置日志配置
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
logger.info("=== 应用启动 ===")
logger.info(f"Python版本: {platform.python_version()}")
logger.info(f"操作系统: {platform.system()} {platform.release()}")

# 检查导入的模块
try:
    logger.info("检查导入的模块...")
    logger.info(f"FastAPI版本: {fastapi.__version__}")
    logger.info(f"SQLAlchemy引擎: {engine}")
    logger.info("模块导入检查完成")
except Exception as e:
    logger.error(f"模块导入检查失败: {e}")
    logger.error(traceback.format_exc())

# 修改为：只在显式指定重置时才重置数据库
if os.environ.get("RESET_DB") == "true":
    logger.warning("重置数据库表...")
    models.Base.metadata.drop_all(bind=engine)
    models.Base.metadata.create_all(bind=engine)
else:
    # 只创建不存在的表
    logger.info("正在创建不存在的数据库表...")
    models.Base.metadata.create_all(bind=engine)
    
    # 检查特定表是否存在并创建
    from app.models import Configuration, Device, TemplateConfig
    from sqlalchemy import inspect, text, Boolean
    
    # 创建一个inspector对象
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()
    
    if "configurations" not in existing_tables:
        logger.warning("configurations表不存在，正在创建...")
        Configuration.__table__.create(engine, checkfirst=True)
    else:
        # 检查表结构并更新缺失的列
        logger.info("检查configurations表结构...")
        columns = [c["name"] for c in inspector.get_columns("configurations")]
        
        # 检查model中的字段是否都存在于数据库表中
        missing_columns = []
        for column in Configuration.__table__.columns:
            if column.name not in columns:
                missing_columns.append(column.name)
        
        if missing_columns:
            logger.warning(f"configurations表缺少以下列: {missing_columns}")
            with engine.connect() as connection:
                for column_name in missing_columns:
                    column = Configuration.__table__.columns[column_name]
                    column_type = column.type.compile(engine.dialect)
                    nullable = "NULL" if column.nullable else "NOT NULL"
                    
                    # 处理不同类型的默认值
                    default_clause = ""
                    if column.default is not None:
                        # 对于布尔类型，转换为0/1
                        if isinstance(column.type, Boolean) and column.default.arg is not None:
                            bool_value = "1" if column.default.arg else "0"
                            default_clause = f"DEFAULT {bool_value}"
                        # 对于字符串类型，添加引号
                        elif hasattr(column.default, 'arg') and column.default.arg is not None:
                            if isinstance(column.default.arg, str):
                                default_clause = f"DEFAULT '{column.default.arg}'"
                            elif isinstance(column.default.arg, (int, float, bool)):
                                default_clause = f"DEFAULT {column.default.arg}"
                    
                    # 构建添加列的SQL
                    sql = f"ALTER TABLE configurations ADD COLUMN {column_name} {column_type} {nullable} {default_clause}".strip()
                    logger.info(f"执行SQL: {sql}")
                    try:
                        connection.execute(text(sql))
                        connection.commit()
                        logger.info(f"成功添加列: {column_name}")
                    except Exception as e:
                        logger.error(f"添加列 {column_name} 失败: {str(e)}")
    
    if "devices" not in existing_tables:
        logger.warning("devices表不存在，正在创建...")
        Device.__table__.create(engine, checkfirst=True)
    
    if "template_configs" not in existing_tables:
        logger.warning("template_configs表不存在，正在创建...")
        TemplateConfig.__table__.create(engine, checkfirst=True)
    
    logger.info("完成数据库表创建检查")

# 创建应用
app = FastAPI(
    title="网络设备配置管理系统",
    description="网络设备配置管理系统后端API",
    version="0.1.0",
    # 启用斜杠重定向，自动处理尾斜杠问题
    redirect_slashes=True
)

# 添加请求日志中间件
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """记录所有请求的中间件"""
    request_id = str(datetime.datetime.now().timestamp())
    start_time = datetime.datetime.now()
    
    # 记录请求信息
    logger.info(f"[{request_id}] 开始请求: {request.method} {request.url}")
    
    # 尝试记录请求体
    try:
        body = await request.body()
        if body:
            logger.debug(f"[{request_id}] 请求体: {body.decode()}")
    except Exception as e:
        logger.debug(f"[{request_id}] 无法读取请求体: {e}")
    
    # 处理请求
    try:
        # 检查是否是assets请求，如果是则重定向到static/assets
        path = request.url.path
        if path.startswith('/assets/'):
            new_path = f"/static{path}"
            logger.info(f"[{request_id}] 重定向资源请求: {path} -> {new_path}")
            from fastapi.responses import RedirectResponse
            return RedirectResponse(url=new_path)
            
        response = await call_next(request)
        process_time = (datetime.datetime.now() - start_time).total_seconds()
        logger.info(f"[{request_id}] 完成请求: {request.method} {request.url} - 状态码: {response.status_code} - 耗时: {process_time:.4f}s")
        return response
    except Exception as e:
        logger.error(f"[{request_id}] 请求处理异常: {e}")
        logger.error(traceback.format_exc())
        raise

# 添加CORS中间件，解决跨域请求问题
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源（在生产环境应该限制为特定域名）
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有HTTP头
    expose_headers=["*"],  # 公开所有响应头
    max_age=600,  # 预检请求缓存10分钟
)

# 添加异常处理中间件
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器，确保所有异常都有CORS头并返回友好消息"""
    import traceback
    
    # 记录错误到日志
    logger.error(f"处理URL {request.url} 时发生未捕获异常: {exc}")
    logger.error(traceback.format_exc())
    
    # 构建错误响应
    status_code = 500
    if isinstance(exc, HTTPException):
        status_code = exc.status_code
    
    # 创建响应
    response = JSONResponse(
        status_code=status_code,
        content={
            "detail": str(exc),
            "path": str(request.url),
            "method": request.method,
            "timestamp": datetime.datetime.now().isoformat(),
        },
    )
    
    # 添加CORS头
    response.headers["Access-Control-Allow-Origin"] = "*"
    response.headers["Access-Control-Allow-Methods"] = "*"
    response.headers["Access-Control-Allow-Headers"] = "*"
    
    return response

# API前缀
API_PREFIX = "/api"



# 直接在main.py中定义统计端点，避免路由器冲突
@app.get(f"{API_PREFIX}/devices/ports/statistics")
def get_device_ports_statistics_direct(db: Session = Depends(get_db)):
    """设备端口统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])
        estimated_total_ports = total_devices * 24
        estimated_used_ports = active_devices * 12
        return {
            "total_ports": estimated_total_ports,
            "used_ports": estimated_used_ports,
            "usage_rate": round((estimated_used_ports / estimated_total_ports * 100) if estimated_total_ports > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {"total_ports": 0, "used_ports": 0, "usage_rate": 0, "error": str(e)}

@app.get(f"{API_PREFIX}/inspection-results/")
def get_inspection_results_direct(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """巡检结果列表"""
    try:
        from app.models import Device
        from datetime import datetime, timedelta
        devices = db.query(Device).offset(skip).limit(limit).all()
        results = []
        for device in devices:
            last_check = device.last_check_time or (datetime.now() - timedelta(hours=1))
            results.append({
                "id": device.id,
                "device_id": device.id,
                "device_name": device.name,
                "ip_address": device.ip_address,
                "status": "online" if device.is_active else "offline",
                "check_time": last_check.isoformat() if last_check else None,
                "cpu_usage": device.cpu_usage or "N/A",
                "memory_usage": device.memory_usage or "N/A",
                "response_time": "10ms" if device.is_active else "timeout"
            })
        total = db.query(Device).count()
        return {"items": results, "total": total, "skip": skip, "limit": limit}
    except Exception as e:
        return {"items": [], "total": 0, "skip": skip, "limit": limit, "error": str(e)}

@app.get(f"{API_PREFIX}/ip-management/statistics")
def get_ip_management_statistics_direct(db: Session = Depends(get_db)):
    """IP管理统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])
        estimated_total_ips = 254 * 10
        estimated_used_ips = total_devices
        return {
            "total_ips": estimated_total_ips,
            "used_ips": estimated_used_ips,
            "available_ips": estimated_total_ips - estimated_used_ips,
            "usage_rate": round((estimated_used_ips / estimated_total_ips * 100) if estimated_total_ips > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {"total_ips": 0, "used_ips": 0, "available_ips": 0, "usage_rate": 0, "error": str(e)}

@app.get(f"{API_PREFIX}/rack-management/statistics")
def get_rack_management_statistics_direct(db: Session = Depends(get_db)):
    """机柜管理统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        devices_with_position = [d for d in devices if d.rack and d.position]
        estimated_total_u = 42 * 20
        estimated_used_u = len(devices_with_position)
        return {
            "total_u": estimated_total_u,
            "used_u": estimated_used_u,
            "available_u": estimated_total_u - estimated_used_u,
            "usage_rate": round((estimated_used_u / estimated_total_u * 100) if estimated_total_u > 0 else 0, 2),
            "total_racks": 20,
            "devices_with_position": len(devices_with_position),
            "devices_without_position": len(devices) - len(devices_with_position)
        }
    except Exception as e:
        return {"total_u": 0, "used_u": 0, "available_u": 0, "usage_rate": 0, "error": str(e)}

# 包含路由器，注意路径的组合
# 注释掉统计路由器，直接使用上面的端点
# app.include_router(statistics_router, prefix=API_PREFIX, tags=["statistics"])

app.include_router(
    device_router,
    prefix=f"{API_PREFIX}/devices",  # 没有尾部斜杠
    tags=["devices"]
)
app.include_router(config_router, prefix=API_PREFIX)
app.include_router(configuration_router, prefix=API_PREFIX, tags=["configurations"])
app.include_router(webssh_router, prefix="/api")
app.include_router(ip_management.router, prefix=API_PREFIX, tags=["ip_management"])
app.include_router(port_monitoring.router, tags=["port_monitoring"])
app.include_router(inspection_router, prefix="/api", tags=["Inspection"])
app.include_router(system_router, prefix="/api", tags=["System Information"])
app.include_router(config_generator_router, prefix=f"{API_PREFIX}/config", tags=["config_generator"])
app.include_router(rack_management_router, prefix=f"{API_PREFIX}/rack-management", tags=["rack_management"])
app.include_router(ai_assistant_router, tags=["AI Assistant"])  # 注册AI助手路由，路由已设置前缀为/api/ai
app.include_router(webssh_ai_router, prefix="/api", tags=["WebSSH AI Assistant"])  # 注册WebSSH AI助手路由
app.include_router(template_router, prefix="/api", tags=["Template Management"])  # 注册模板管理路由，前缀为/api
app.include_router(device_ports_router)  # 注册设备端口路由，已在路由中设置前缀
app.include_router(license_router)  # 注册授权管理路由，路由中已设置前缀为/api/license
app.include_router(batch_ping_router)  # 注册批量ping路由，路由中已设置前缀为/api
app.include_router(vlan_router)  # 注册VLAN管理路由
app.include_router(datacenter_router, prefix=f"{API_PREFIX}/datacenter", tags=["datacenter"])  # 注册机房管理路由
app.include_router(ai_operations_router, tags=["AI Operations"])  # 注册AI运维路由，路由中已设置前缀为/api/ai-operations
app.include_router(network_tools_router, tags=["Network Tools"])  # 注册网络工具路由，路由中已设置前缀为/api/network
app.include_router(ip_conflict_router, prefix=f"{API_PREFIX}/ip-conflicts", tags=["IP Conflict Detection"])  # 注册IP冲突检测路由
app.include_router(device_ip_integration_router, prefix=f"{API_PREFIX}/integration", tags=["Device IP Integration"])  # 注册设备IP集成路由
app.include_router(config_monitoring.router, prefix=f"{API_PREFIX}/config-monitoring", tags=["Config Monitoring"])  # 注册配置监控路由

# 添加主路由器，包含拓扑和设备组路由
app.include_router(router, prefix=API_PREFIX)

# 添加测试路由
app.include_router(test_routes_router, prefix=API_PREFIX)

# 定义WebSSH路由 - 必须在静态文件挂载之前定义
@app.get("/webssh")
async def get_webssh(request: Request):
    """提供WebSSH页面 - 重定向到前端SPA路由"""
    from fastapi.responses import RedirectResponse, FileResponse, HTMLResponse
    import os

    # 检查是否存在前端构建文件
    static_dir = "app/static/dist"
    index_path = os.path.join(static_dir, "index.html")

    if os.path.exists(index_path):
        # 如果前端已构建，直接返回index.html，让前端路由处理
        # 前端会根据URL路径 /webssh 和查询参数来处理WebSSH页面
        return FileResponse(index_path)
    else:
        # 如果前端未构建，返回错误信息
        return HTMLResponse(f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>WebSSH终端错误</title>
        </head>
        <body>
            <h1>WebSSH终端加载错误</h1>
            <p>前端文件未找到，请确保前端已正确构建</p>
            <p>查询参数: {dict(request.query_params)}</p>
        </body>
        </html>
        """)

@app.get("/webssh-test")
async def webssh_test():
    """测试WebSSH路由是否工作"""
    return {"status": "ok", "message": "WebSSH路由测试成功", "route": "/webssh"}

# 打印所有路由用于调试
print("=== 所有注册的路由 ===")
for route in app.routes:
    if hasattr(route, 'path'):
        methods = getattr(route, 'methods', ['GET'])
        print(f"路由: {route.path}, 方法: {methods}, 名称: {getattr(route, 'name', 'unnamed')}")
print("=== 路由列表结束 ===")

# 注释掉这两行
# app.router.route_class = APIRoute
# app.router.route_class.set_defaults(priority=1)  # 设置默认优先级

# 挂载静态文件(如果存在)
import sys

# 根据运行环境确定静态文件目录
# if getattr(sys, 'frozen', False):
#     # 打包环境：需要区分nuitka和PyInstaller
#     base_dir = os.path.dirname(sys.executable)
    
#     # 检查是否为nuitka打包（直接在app/static/dist）
#     nuitka_static_dir = os.path.join(base_dir, "app", "static", "dist")
#     # 检查是否为PyInstaller打包（在_internal/app/static/dist）
#     pyinstaller_static_dir = os.path.join(base_dir, "_internal", "app", "static", "dist")
    
#     if os.path.exists(nuitka_static_dir):
#         static_dir = nuitka_static_dir
#     elif os.path.exists(pyinstaller_static_dir):
#         static_dir = pyinstaller_static_dir
#     else:
#         # 如果都不存在，默认使用nuitka路径
#         static_dir = nuitka_static_dir
# else:
#     # 开发环境：静态文件在 ../frontend/dist
#     static_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "../frontend/dist")

# if os.path.exists(static_dir):
#     # 在 StaticFiles 挂载之前添加一个明确的 SPA 回退处理
#     @app.get("/{full_path:path}")
#     async def serve_spa(full_path: str):
#         # 如果是 API 路径，跳过
#         if full_path.startswith("api/"):
#             raise HTTPException(status_code=404, detail="Not found")
        
#         # 检查文件是否存在
#         file_path = os.path.join(static_dir, full_path)
#         if os.path.isfile(file_path):
#             return FileResponse(file_path)
        
#         # 对于所有其他路径，返回 index.html（SPA 回退）
#         index_path = os.path.join(static_dir, "index.html")
#         if os.path.exists(index_path):
#             return FileResponse(index_path)
        
#         raise HTTPException(status_code=404, detail="Not found")
    
    # 注释掉原来的 StaticFiles 挂载
app.mount("/static", StaticFiles(directory="app/static/dist"), name="static")

# 应用启动事件
@app.on_event("startup")
async def startup_db_client():
    try:
        from app.init_db import init_database  # 修正为正确的函数名
        from app.init_rack_management import init_rack_management
        from app.init_ip_database import initialize_ip_database
        
        print("正在初始化数据库...")
        init_database()  # 使用正确的函数名
        
        print("正在初始化机柜管理表...")
        init_rack_management()
        
        print("正在初始化IP归属地数据库...")
        initialize_ip_database()
        
        print("数据库初始化完成")
    except Exception as e:
        print(f"数据库初始化失败: {e}")
        traceback.print_exc()
    
    # 更详细地打印所有路由信息
    routes_info = []
    for route in app.routes:
        methods = getattr(route, "methods", ["GET"])
        path = getattr(route, "path", "unknown")
        name = getattr(route, "name", "unnamed")
        routes_info.append(f"路由: {path}, 方法: {methods}, 名称: {name}")
    
    # 按路径排序
    routes_info.sort()
    for route_info in routes_info:
        logger.info(route_info)
    
    # 检查可能冲突的路由
    all_paths = [getattr(route, "path", "") for route in app.routes]
    duplicate_paths = set([p for p in all_paths if all_paths.count(p) > 1])
    
    if duplicate_paths:
        logger.warning(f"发现可能冲突的路由路径: {duplicate_paths}")
    
    # 检查数据库连接
    db_ok = check_database_connection()
    if not db_ok:
        logger.error("警告: 数据库连接失败! MySQL服务可能未启动或连接字符串有误。")
        logger.info("数据库连接字符串: " + os.environ.get("DATABASE_URL", "未设置"))
    else:
        logger.info("数据库连接正常")

    # 测试数据库查询
    try:
        from app.database import SessionLocal
        from app.models import Device
        
        with SessionLocal() as db:
            # 测试查询设备
            devices = db.query(Device).all()
            logger.info(f"数据库测试: 找到 {len(devices)} 台设备")
            
            if devices:
                for device in devices:
                    logger.info(f"设备: {device.name} ({device.ip_address}) - 类型: {device.device_type}, 厂商: {device.manufacturer}")
            
            # 如果没有设备，创建多个测试设备
            if not devices:
                logger.warning("数据库中没有设备，创建多个测试设备")
                test_devices = [
                    Device(
                        name="测试路由器",
                        ip_address="***********",
                        device_type="router",
                        manufacturer="cisco",
                        model="Test Model",
                        username="admin",
                        password="admin123",
                        is_active=True
                    ),
                    Device(
                        name="测试交换机",
                        ip_address="***********",
                        device_type="switch",
                        manufacturer="huawei",
                        model="S5700",
                        username="admin",
                        password="huawei123",
                        is_active=True
                    ),
                    Device(
                        name="测试防火墙",
                        ip_address="***********54",
                        device_type="firewall",
                        manufacturer="h3c",
                        model="F1000",
                        username="admin",
                        password="h3c123",
                        is_active=True
                    )
                ]
                
                for device in test_devices:
                    db.add(device)
                
                db.commit()
                logger.info(f"创建了 {len(test_devices)} 台测试设备")
    except Exception as e:
        logger.error(f"数据库测试失败: {str(e)}")

    # 添加端口监控路由的日志
    logger.info("正在注册端口监控路由...")
    try:
        # 确保端口监控路由已注册
        port_monitoring_routes = [route for route in app.routes 
                                if route.path.startswith("/api/check-port") or 
                                   route.path.startswith("/api/trace-route")]
        
        logger.info(f"已注册端口监控路由: {len(port_monitoring_routes)} 个")
        for route in port_monitoring_routes:
            logger.info(f"  - {route.path} [{', '.join(route.methods)}]")
    except Exception as e:
        logger.error(f"注册端口监控路由时出错: {str(e)}")

@app.get(f"{API_PREFIX}/health")
def health_check():
    """健康检查端点"""
    return {"status": "healthy"}

@app.get(f"{API_PREFIX}/test")
def test_api():
    """测试API是否正常工作"""
    return {"status": "ok", "message": "API测试成功"}

@app.get(f"{API_PREFIX}/ping")
def ping():
    """简单的测试端点，不需要任何参数"""
    return {"ping": "pong", "time": datetime.datetime.now().isoformat()}

@app.get(f"{API_PREFIX}/debug")
def debug_info():
    """返回服务器调试信息"""
    routes = [{"path": route.path, "methods": getattr(route, "methods", ["GET"])} 
              for route in app.routes if hasattr(route, "path")]
    
    return {
        "routes": routes,
        "registered_device_routes": [r["path"] for r in routes if "/devices" in r["path"]],
        "server_time": datetime.datetime.now().isoformat(),
        "python_version": platform.python_version(),
        "fastapi_version": fastapi.__version__
    }

@app.get(f"{API_PREFIX}/db-check")
def db_check():
    """检查数据库连接和数据"""
    try:
        from sqlalchemy.orm import Session
        from app.database import SessionLocal
        from app.models import Device
        
        with SessionLocal() as db:
            device_count = db.query(Device).count()
            devices = db.query(Device).limit(3).all()
            
            device_samples = []
            for device in devices:
                device_samples.append({
                    "id": device.id,
                    "name": device.name,
                    "ip_address": device.ip_address,
                    "device_type": device.device_type,
                    "manufacturer": device.manufacturer
                })
            
            return {
                "status": "ok",
                "database_connected": True,
                "device_count": device_count,
                "device_samples": device_samples
            }
    except Exception as e:
        return {
            "status": "error",
            "database_connected": False,
            "error": str(e)
        }

# 如果需要在启动时初始化数据库，取消下面注释
# init_database()

@app.get("/")
async def root():
    """返回前端主页"""
    from fastapi.responses import FileResponse
    import os
    
    # 查找index.html文件
    static_dir = "app/static/dist"
    index_path = os.path.join(static_dir, "index.html")
    
    if os.path.exists(index_path):
        return FileResponse(index_path)
    else:
        # 如果找不到前端文件，返回错误信息
        return {"error": "前端文件未找到", "message": "请确保前端已正确构建"}

# 添加license页面处理
@app.get("/license")
async def license_page():
    """将license路径重定向到主页"""
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/")

@app.get(f"{API_PREFIX}")
def read_api_root():
    """API根路径"""
    return {
        "message": "欢迎使用网络设备管理API",
        "version": "0.1.0",
        "documentation": "/docs",
        "status": "running"
    }

@app.get("/api/devices-test")
def devices_test():
    """简单测试设备API是否可访问"""
    return {
        "status": "ok", 
        "message": "设备API测试成功", 
        "timestamp": datetime.datetime.now().isoformat(),
        "routes": [
            {"path": "/api/devices/", "description": "获取所有设备"},
            {"path": "/api/devices/{device_id}", "description": "获取单个设备"},
            {"path": "/api/devices/refresh-status", "description": "刷新设备状态"}
        ]
    }

# 添加简单的 API 根路径处理
@app.get("/api/")
def api_root():
    """API 根路径"""
    return {
        "message": "欢迎使用设备管理 API",
        "version": "1.0.0",
        "timestamp": datetime.datetime.now().isoformat(),
        "endpoints": [
            "/api/devices/",
            "/api/health",
            "/api/ping"
        ]
    }

# 添加测试端点
@app.get("/api/test/")
def api_test():
    """API 测试端点"""
    return {
        "status": "ok",
        "message": "API 测试端点正常工作",
        "timestamp": datetime.datetime.now().isoformat()
    }

# 添加调试端点
@app.get("/api/debug/")
def api_debug():
    """API 调试端点"""
    # 收集所有路由信息
    routes = []
    for route in app.routes:
        routes.append({
            "path": route.path,
            "name": route.name,
            "methods": list(route.methods) if route.methods else []
        })
    
    return {
        "status": "ok",
        "routes": routes,
        "timestamp": datetime.datetime.now().isoformat()
    }

@app.get(f"{API_PREFIX}/router-check")
def router_check():
    """检查路由器配置是否正确"""
    try:
        from app.routers.device import router as device_router
        
        device_routes = []
        for route in device_router.routes:
            device_routes.append({
                "path": route.path,
                "methods": list(route.methods) if hasattr(route, "methods") and route.methods else ["GET"],
                "name": route.name,
                "endpoint": route.endpoint.__name__ if hasattr(route, "endpoint") else "unknown"
            })
        
        return {
            "status": "ok",
            "prefix": f"{API_PREFIX}/devices",
            "device_routes": device_routes,
            "full_paths": [f"{API_PREFIX}/devices{route.path}" for route in device_router.routes],
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e)
        }

# 添加便利的刷新状态端点
@app.post(f"{API_PREFIX}/devices/refresh-status")
def refresh_devices_status(db: Session = Depends(get_db)):
    """刷新设备状态并优化响应时间"""
    try:
        # 获取当前所有设备
        from app.models import Device
        import asyncio
        import concurrent.futures
        from app.utils.device_connector import DeviceConnector
        
        # 记录开始时间以计算执行时间
        start_time = datetime.datetime.now()
        
        # 获取所有设备，但限制数量以避免超时
        devices = db.query(Device).limit(50).all()
        if not devices:
            return {
                "status": "warning",
                "message": "没有找到设备",
                "updated": 0,
                "total": 0,
                "execution_time_ms": 0
            }
        
        # 使用线程池并行检查设备状态
        updated_count = 0
        total_count = len(devices)
        
        # 创建一个线程池来并行检查设备
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            # 创建一个任务列表，每个任务检查一个设备
            futures = []
            for device in devices:
                future = executor.submit(
                    DeviceConnector.check_device_online,
                    device.ip_address
                )
                futures.append((device, future))
            
            # 处理结果
            for device, future in futures:
                try:
                    is_online = future.result(timeout=5)  # 5秒超时
                    if device.is_active != is_online:
                        device.is_active = is_online
                        updated_count += 1
                except Exception as e:
                    logger.error(f"检查设备 {device.name} ({device.ip_address}) 状态失败: {e}")
        
        # 提交更改
        db.commit()
        
        # 计算执行时间
        end_time = datetime.datetime.now()
        execution_time = (end_time - start_time).total_seconds() * 1000
        
        return {
            "status": "success",
            "message": f"已刷新 {total_count} 台设备状态，{updated_count} 台设备状态发生变化",
            "updated": updated_count,
            "total": total_count,
            "execution_time_ms": execution_time
        }
    except Exception as e:
        import traceback
        logger.error(f"刷新设备状态失败: {str(e)}")
        logger.error(traceback.format_exc())
        
        return {
            "status": "error",
            "message": f"刷新设备状态失败: {str(e)}",
            "error_details": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/check-devices")
def check_all_devices():
    """全面检查设备数据库"""
    try:
        from app.database import SessionLocal
        from app.models import Device
        import traceback
        
        result = {
            "status": "checking",
            "database_info": {
                "url": os.environ.get("DATABASE_URL", "未设置").replace(":Admin123@", ":***@")  # 隐藏密码
            },
            "tables": [],
            "devices": [],
            "error": None
        }
        
        try:
            # 检查表结构
            from sqlalchemy import inspect
            inspector = inspect(engine)
            result["tables"] = inspector.get_table_names()
            result["database_info"]["tables_exist"] = "device" in result["tables"]
            
            # 获取device表结构
            if "device" in result["tables"]:
                result["database_info"]["device_columns"] = [
                    {"name": column["name"], "type": str(column["type"])}
                    for column in inspector.get_columns("device")
                ]
        except Exception as e:
            result["error"] = f"检查表结构失败: {str(e)}"
            return result
        
        # 查询设备数据
        try:
            with SessionLocal() as db:
                devices = db.query(Device).all()
                result["device_count"] = len(devices)
                
                # 转换为可JSON序列化的格式
                for device in devices:
                    result["devices"].append({
                        "id": device.id,
                        "name": device.name,
                        "ip_address": device.ip_address,
                        "device_type": device.device_type,
                        "manufacturer": device.manufacturer,
                        "model": device.model,
                        "is_active": device.is_active,
                        "created_at": device.created_at.isoformat() if device.created_at else None,
                        "updated_at": device.updated_at.isoformat() if device.updated_at else None
                    })
                
                result["status"] = "ok"
                
                # 如果没有设备，提示用户
                if not devices:
                    result["suggestion"] = "数据库中没有设备，请访问 /api/add-test-devices 或 /api/reset-database 创建测试设备"
        except Exception as e:
            result["error"] = f"查询设备失败: {str(e)}"
            result["traceback"] = traceback.format_exc()
            result["status"] = "error"
            
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"检查设备失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.post(f"{API_PREFIX}/add-test-devices")
def add_test_devices():
    """添加一些测试设备"""
    try:
        from app.database import SessionLocal
        from app.models import Device
        import random
        
        test_devices = [
            {
                "name": "FW-North-01",
                "ip_address": "***********",
                "device_type": "firewall",
                "manufacturer": "huawei",
                "model": "USG6000",
                "username": "admin",
                "password": "Admin@123",
                "is_active": random.choice([True, False])
            },
            {
                "name": "Access-SW-Floor1",
                "ip_address": "************",
                "device_type": "switch",
                "manufacturer": "h3c",
                "model": "S5120",
                "username": "admin",
                "password": "Admin@123",
                "is_active": random.choice([True, False])
            },
            {
                "name": "Access-SW-Floor2",
                "ip_address": "************",
                "device_type": "switch",
                "manufacturer": "h3c",
                "model": "S5120",
                "username": "admin",
                "password": "Admin@123",
                "is_active": random.choice([True, False])
            }
        ]
        
        with SessionLocal() as db:
            for device_data in test_devices:
                # 检查是否已存在相同IP的设备
                existing = db.query(Device).filter(Device.ip_address == device_data["ip_address"]).first()
                if not existing:
                    device = Device(**device_data)
                    db.add(device)
                
            db.commit()
            
        return {"status": "success", "message": f"已添加 {len(test_devices)} 台测试设备"}
    except Exception as e:
        return {"status": "error", "message": f"添加测试设备失败: {str(e)}"}

@app.get(f"{API_PREFIX}/db-diagnostic")
def db_diagnostic():
    """详细诊断数据库问题"""
    try:
        from sqlalchemy.orm import Session
        from app.database import SessionLocal, engine
        from app.models import Device
        import sqlalchemy as sa
        
        result = {
            "status": "checking",
            "connection": False,
            "tables": [],
            "device_table_exists": False,
            "devices": [],
            "errors": []
        }
        
        # 检查连接
        try:
            conn = engine.connect()
            result["connection"] = True
            conn.close()
        except Exception as e:
            result["errors"].append(f"数据库连接错误: {str(e)}")
            return result
        
        # 检查表是否存在
        try:
            from sqlalchemy import inspect
            inspector = inspect(engine)
            result["tables"] = inspector.get_table_names()
            result["device_table_exists"] = "device" in result["tables"]
        except Exception as e:
            result["errors"].append(f"检查表结构错误: {str(e)}")
        
        # 尝试查询设备
        try:
            with SessionLocal() as db:
                # 获取设备数量
                device_count = db.query(Device).count()
                result["device_count"] = device_count
                
                # 获取所有设备
                all_devices = db.query(Device).all()
                
                # 转换为可JSON序列化的格式
                devices_list = []
                for device in all_devices:
                    devices_list.append({
                        "id": device.id,
                        "name": device.name,
                        "ip_address": device.ip_address,
                        "device_type": device.device_type,
                        "manufacturer": device.manufacturer,
                        "model": device.model,
                        "is_active": device.is_active
                    })
                
                result["devices"] = devices_list
        except Exception as e:
            result["errors"].append(f"查询设备失败: {str(e)}")
        
        result["status"] = "ok" if not result["errors"] else "error"
        return result
    except Exception as e:
        return {
            "status": "error",
            "message": f"诊断失败: {str(e)}"
        }

@app.post(f"{API_PREFIX}/create-device")
def create_device_direct():
    """直接创建设备，绕过常规API路由"""
    try:
        from app.database import SessionLocal
        from app.models import Device
        import random
        import datetime
        
        # 创建一个设备记录
        new_device = {
            "name": f"测试设备-{datetime.datetime.now().strftime('%H%M%S')}",
            "ip_address": f"192.168.{random.randint(1,254)}.{random.randint(1,254)}",
            "device_type": random.choice(["router", "switch", "firewall"]),
            "manufacturer": random.choice(["cisco", "huawei", "h3c"]),
            "model": f"Model-{random.randint(1000, 9999)}",
            "username": "admin",
            "password": "Admin@123",
            "is_active": random.choice([True, False])
        }
        
        with SessionLocal() as db:
            # 检查IP是否已存在
            existing = db.query(Device).filter(Device.ip_address == new_device["ip_address"]).first()
            if existing:
                return {
                    "status": "error", 
                    "message": f"IP地址 {new_device['ip_address']} 已被设备 {existing.name} 使用"
                }
            
            # 创建新设备
            device = Device(**new_device)
            db.add(device)
            db.commit()
            db.refresh(device)
            
            return {
                "status": "success",
                "message": f"已创建设备 {device.name}",
                "device": {
                    "id": device.id,
                    "name": device.name,
                    "ip_address": device.ip_address,
                    "device_type": device.device_type,
                    "manufacturer": device.manufacturer,
                    "model": device.model,
                    "is_active": device.is_active
                }
            }
    except Exception as e:
        return {"status": "error", "message": f"创建设备失败: {str(e)}"}

@app.post(f"{API_PREFIX}/reset-database")
def reset_database():
    """重置数据库并创建初始数据"""
    try:
        from app.database import engine
        from app.models import Base, Device
        from app.database import SessionLocal
        import random
        
        # 删除所有表并重新创建
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        
        # 创建初始数据
        initial_devices = [
            {
                "name": "Core-Router-01",
                "ip_address": "***********",
                "device_type": "router",
                "manufacturer": "cisco",
                "model": "ASR 9000",
                "username": "admin",
                "password": "Admin@123",
                "is_active": True
            },
            {
                "name": "Firewall-Main",
                "ip_address": "***********",
                "device_type": "firewall",
                "manufacturer": "huawei",
                "model": "USG6000",
                "username": "admin",
                "password": "Admin@123",
                "is_active": True
            },
            {
                "name": "Switch-Floor1",
                "ip_address": "***********",
                "device_type": "switch",
                "manufacturer": "h3c",
                "model": "S5820X",
                "username": "admin",
                "password": "Admin@123",
                "is_active": True
            }
        ]
        
        with SessionLocal() as db:
            for device_data in initial_devices:
                device = Device(**device_data)
                db.add(device)
            db.commit()
            
        return {
            "status": "success", 
            "message": f"数据库已重置，已创建 {len(initial_devices)} 台初始设备"
        }
    except Exception as e:
        return {"status": "error", "message": f"重置数据库失败: {str(e)}"}

# 添加一个直接的设备列表端点，便于测试
@app.get(f"{API_PREFIX}/direct-devices")
def get_direct_devices(db: Session = Depends(get_db)):
    """返回所有设备数据，包括机房、机柜、U位信息"""
    try:
        from app.models import Device
        
        # 查询所有设备
        devices = db.query(Device).all()
        
        # 转换为可序列化的格式，确保包含所有字段
        result = []
        for device in devices:
            result.append({
                "id": device.id,
                "name": device.name,
                "ip_address": device.ip_address,
                "device_type": device.device_type,
                "manufacturer": device.manufacturer,
                "model": device.model,
                "is_active": device.is_active,
                "protocol": device.protocol,
                "port": device.port,
                "created_at": device.created_at.isoformat() if device.created_at else None,
                "updated_at": device.updated_at.isoformat() if device.updated_at else None,
                "username": device.username,
                "password": device.password,
                "cpu_usage": device.cpu_usage,
                "memory_usage": device.memory_usage,
                "version_info": device.version_info,
                # 添加位置相关字段
                "datacenter": device.datacenter,
                "rack": device.rack,
                "position": device.position,
                # 添加序列号字段
                "serial_number": device.serial_number
            })
        
        # 直接返回设备列表
        return result
    except Exception as e:
        import traceback
        return {
            "error": str(e),
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/fixed-devices")
def get_fixed_devices():
    """返回固定的设备数据，不依赖数据库"""
    return [
        {
            "id": 1,
            "name": "Core-Router-01",
            "ip_address": "***********",
            "device_type": "router",
            "manufacturer": "cisco",
            "model": "ASR 9000",
            "is_active": True,
            "username": "admin",
            "password": "******"
        },
        {
            "id": 2,
            "name": "Firewall-Main",
            "ip_address": "***********",
            "device_type": "firewall",
            "manufacturer": "huawei",
            "model": "USG6000",
            "is_active": True,
            "username": "admin",
            "password": "******"
        },
        {
            "id": 3,
            "name": "Switch-Floor1",
            "ip_address": "***********",
            "device_type": "switch",
            "manufacturer": "h3c",
            "model": "S5820X",
            "is_active": False,
            "username": "admin",
            "password": "******"
        }
    ]

@app.get(f"{API_PREFIX}/config-check")
def config_check():
    """检查配置是否正确加载"""
    import sys
    import os
    
    # 检查环境变量
    env_vars = {k: v for k, v in os.environ.items() if k.startswith(("DATABASE", "API", "DEBUG"))}
    # 隐藏敏感信息
    if "DATABASE_URL" in env_vars:
        env_vars["DATABASE_URL"] = env_vars["DATABASE_URL"].replace(":Admin123@", ":***@")
    
    # 获取Python路径
    python_paths = sys.path
    
    return {
        "status": "ok",
        "env_vars": env_vars,
        "python_path": python_paths,
        "current_directory": os.getcwd(),
        "api_prefix": API_PREFIX,
        "dotenv_loaded": os.path.exists(".env")
    }



@app.post(f"{API_PREFIX}/devices/safe-delete/{{device_id}}")
def safe_delete_device(device_id: int, db: Session = Depends(get_db)):
    """安全删除设备，避开ORM关系查询"""
    try:
        from sqlalchemy import text
        
        # 修正表名为devices (原来是device)
        db.execute(text(f"DELETE FROM configurations WHERE device_id = {device_id}"))
        result = db.execute(text(f"DELETE FROM devices WHERE id = {device_id}"))
        
        affected_rows = result.rowcount
        db.commit()
        
        if affected_rows > 0:
            return {
                "status": "success",
                "message": f"设备 (ID: {device_id}) 及其配置已成功删除",
                "affected_rows": affected_rows
            }
        else:
            return {
                "status": "warning",
                "message": f"设备ID {device_id} 不存在或已被删除"
            }
    except Exception as e:
        db.rollback()
        import traceback
        return {
            "status": "error",
            "message": f"删除设备失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/methods-check")
def check_methods_support():
    """检查服务器支持的HTTP方法"""
    routes = []
    
    # 查找所有支持DELETE方法的路由
    for route in app.routes:
        if hasattr(route, "methods") and "DELETE" in route.methods:
            routes.append({
                "path": route.path,
                "methods": list(route.methods),
                "endpoint": route.endpoint.__name__ if hasattr(route, "endpoint") else "unknown"
            })
    
    return {
        "status": "ok",
        "delete_routes": routes,
        "cors_config": {
            "allow_origins": "*",
            "allow_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
            "allow_headers": "*",
        }
    }

@app.post(f"{API_PREFIX}/devices/remove/{{device_id}}")
def remove_device_post(device_id: int, db: Session = Depends(get_db)):
    """通过POST请求删除设备（备用方法）"""
    try:
        from app.models import Device
        
        device = db.query(Device).filter(Device.id == device_id).first()
        if not device:
            return {"status": "error", "message": f"设备ID {device_id} 不存在"}
        
        device_name = device.name
        db.delete(device)
        db.commit()
        
        return {
            "status": "success",
            "message": f"设备 '{device_name}' 已成功删除",
            "note": "此删除操作使用POST请求执行"
        }
    except Exception as e:
        return {"status": "error", "message": f"删除设备失败: {str(e)}"}

@app.post(f"{API_PREFIX}/devices/purge/{{device_id}}")
def purge_device(device_id: int):
    """完全清除设备及相关数据"""
    try:
        from app.database import engine
        from sqlalchemy import text
        
        with engine.connect() as connection:
            # 开始事务
            with connection.begin():
                # 删除配置
                connection.execute(text(f"DELETE FROM configurations WHERE device_id = {device_id}"))
                
                # 删除设备
                result = connection.execute(text(f"DELETE FROM device WHERE id = {device_id}"))
                affected = result.rowcount
                
            return {
                "status": "success",
                "message": f"设备及相关数据已清除 (ID: {device_id})",
                "device_deleted": affected > 0
            }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"清除设备失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/check-tables")
def check_tables():
    """检查数据库表结构和表名"""
    try:
        from sqlalchemy import inspect, text
        from app.database import engine, SessionLocal
        
        inspector = inspect(engine)
        tables = inspector.get_table_names()
        
        # 查询每个表中的记录数
        counts = {}
        with engine.connect() as connection:
            for table in tables:
                result = connection.execute(text(f"SELECT COUNT(*) FROM {table}"))
                counts[table] = result.scalar()
        
        # 检查models中定义的表名是否与数据库中实际表名匹配
        model_tables = {
            "Device": {"defined_name": "devices", "exists": "devices" in tables},
            "Configuration": {"defined_name": "configurations", "exists": "configurations" in tables}
        }
        
        # 专门检查设备表
        devices_data = []
        if "devices" in tables or "device" in tables:
            # 确定正确的表名
            device_table = "devices" if "devices" in tables else "device"
            
            with engine.connect() as conn:
                result = conn.execute(text(f"SELECT * FROM {device_table} LIMIT 10"))
                columns = result.keys()
                
                for row in result:
                    device = {}
                    for i, column in enumerate(columns):
                        device[column] = row[i]
                    devices_data.append(device)
        
        return {
            "status": "ok",
            "tables": tables,
            "record_counts": counts,
            "model_tables": model_tables,
            "devices_data": devices_data,
            "note": "如果发现表名不匹配问题，请修改模型或数据库"
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"检查表失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.post(f"{API_PREFIX}/devices/orm-delete/{{device_id}}")
def orm_delete_device(device_id: int, db: Session = Depends(get_db)):
    """使用ORM方式安全删除设备"""
    try:
        from app.models import Device, Configuration
        import sqlalchemy as sa
        
        # 先删除关联的配置
        db.query(Configuration).filter(Configuration.device_id == device_id).delete()
        
        # 再删除设备本身
        device = db.query(Device).get(device_id)
        if not device:
            return {"status": "warning", "message": f"设备ID {device_id} 不存在"}
        
        device_name = device.name
        db.delete(device)
        db.commit()
        
        return {
            "status": "success",
            "message": f"设备 '{device_name}' (ID: {device_id}) 已成功删除"
        }
    except Exception as e:
        db.rollback()
        import traceback
        return {
            "status": "error",
            "message": f"ORM删除设备失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/devices")
def get_all_devices_with_pagination(
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取设备列表，支持分页"""
    try:
        from app.models import Device
        
        # 查询总记录数
        total = db.query(Device).count()
        logger.info(f"设备总数: {total}, 请求页码: {page}, 每页数量: {pageSize}")
        
        # 计算偏移量
        skip = (page - 1) * pageSize
        
        # 查询分页数据
        devices = db.query(Device).offset(skip).limit(pageSize).all()
        logger.info(f"查询返回 {len(devices)} 条记录")
        
        # 格式化响应数据
        result = []
        for device in devices:
            result.append({
                "id": device.id,
                "name": device.name,
                "ip_address": device.ip_address,
                "device_type": device.device_type,
                "manufacturer": device.manufacturer,
                "model": device.model,
                "is_active": device.is_active
            })
        
        # 返回标准分页格式
        return {
            "data": result,
            "pagination": {
                "current": page,
                "pageSize": pageSize,
                "total": total,
                "totalPages": (total + pageSize - 1) // pageSize
            }
        }
    except Exception as e:
        logger.error(f"获取设备列表失败: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return {"status": "error", "message": f"获取设备列表失败: {str(e)}"}

@app.get(f"{API_PREFIX}/pagination-test")
def pagination_test(db: Session = Depends(get_db)):
    """用于测试分页的端点"""
    try:
        from app.models import Device
        
        device_count = db.query(Device).count()
        
        # 模拟不同页码和页面大小的分页结果
        page_sizes = [10, 20, 50]
        results = {}
        
        for size in page_sizes:
            page_count = (device_count + size - 1) // size
            pages_data = {}
            
            for page in range(1, min(page_count + 1, 4)):  # 最多显示前3页
                offset = (page - 1) * size
                devices = db.query(Device).offset(offset).limit(size).all()
                
                pages_data[f"page_{page}"] = {
                    "count": len(devices),
                    "samples": [d.name for d in devices[:3]]
                }
            
            results[f"size_{size}"] = {
                "total_pages": page_count,
                "total_records": device_count,
                "pages": pages_data
            }
            
        return {
            "status": "ok",
            "device_count": device_count,
            "pagination_examples": results
        }
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"测试分页失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/simple-devices")
def get_simple_devices(db: Session = Depends(get_db)):
    """最简单的设备列表格式，返回纯设备数组"""
    try:
        from app.models import Device
        
        # 查询所有设备
        devices = db.query(Device).all()
        
        # 转换为可序列化的格式，包含MAC地址字段
        result = []
        for device in devices:
            result.append({
                "id": device.id,
                "name": device.name,
                "ip_address": device.ip_address,
                "device_type": device.device_type,
                "manufacturer": device.manufacturer,
                "model": device.model,
                "is_active": device.is_active,
                # 添加MAC地址相关字段
                "primary_mac": device.primary_mac,
                "additional_macs": device.additional_macs,
                "mac_status": device.mac_status,
                "mac_last_updated": device.mac_last_updated.isoformat() if device.mac_last_updated else None
            })
        
        # 直接返回数组
        return result
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"获取简单设备列表失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get(f"{API_PREFIX}/statistics/device-ports")
def get_device_ports_statistics(db: Session = Depends(get_db)):
    """获取设备端口统计信息"""
    try:
        from app.models import Device

        # 查询所有设备
        devices = db.query(Device).all()

        # 基于设备数量估算端口统计
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])

        # 估算端口数量（每个设备平均24个端口）
        estimated_total_ports = total_devices * 24
        estimated_used_ports = active_devices * 12  # 假设50%使用率

        return {
            "total_ports": estimated_total_ports,
            "used_ports": estimated_used_ports,
            "usage_rate": round((estimated_used_ports / estimated_total_ports * 100) if estimated_total_ports > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {
            "total_ports": 0,
            "used_ports": 0,
            "usage_rate": 0,
            "error": str(e)
        }

@app.get(f"{API_PREFIX}/statistics/inspection-results")
def get_inspection_results(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """获取巡检结果列表"""
    try:
        from app.models import Device
        from datetime import datetime, timedelta

        # 查询设备作为巡检结果的基础
        devices = db.query(Device).offset(skip).limit(limit).all()

        # 模拟巡检结果
        results = []
        for device in devices:
            # 模拟最近的巡检时间
            last_check = device.last_check_time or (datetime.now() - timedelta(hours=1))

            results.append({
                "id": device.id,
                "device_id": device.id,
                "device_name": device.name,
                "ip_address": device.ip_address,
                "status": "online" if device.is_active else "offline",
                "check_time": last_check.isoformat() if last_check else None,
                "cpu_usage": device.cpu_usage or "N/A",
                "memory_usage": device.memory_usage or "N/A",
                "response_time": "10ms" if device.is_active else "timeout"
            })

        # 获取总数
        total = db.query(Device).count()

        return {
            "items": results,
            "total": total,
            "skip": skip,
            "limit": limit
        }
    except Exception as e:
        return {
            "items": [],
            "total": 0,
            "skip": skip,
            "limit": limit,
            "error": str(e)
        }

@app.get(f"{API_PREFIX}/statistics/ip-management")
def get_ip_management_statistics(db: Session = Depends(get_db)):
    """获取IP管理统计信息"""
    try:
        from app.models import Device

        # 查询所有设备
        devices = db.query(Device).all()

        # 基于设备IP地址统计
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])

        # 估算IP使用情况
        estimated_total_ips = 254 * 10  # 假设10个C类网段
        estimated_used_ips = total_devices

        return {
            "total_ips": estimated_total_ips,
            "used_ips": estimated_used_ips,
            "available_ips": estimated_total_ips - estimated_used_ips,
            "usage_rate": round((estimated_used_ips / estimated_total_ips * 100) if estimated_total_ips > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {
            "total_ips": 0,
            "used_ips": 0,
            "available_ips": 0,
            "usage_rate": 0,
            "error": str(e)
        }

@app.get(f"{API_PREFIX}/statistics/rack-management")
def get_rack_management_statistics(db: Session = Depends(get_db)):
    """获取机柜管理统计信息"""
    try:
        from app.models import Device

        # 查询所有设备
        devices = db.query(Device).all()

        # 基于设备位置信息统计机柜使用情况
        devices_with_position = [d for d in devices if d.rack and d.position]

        # 估算机柜使用情况
        estimated_total_u = 42 * 20  # 假设20个42U机柜
        estimated_used_u = len(devices_with_position)

        return {
            "total_u": estimated_total_u,
            "used_u": estimated_used_u,
            "available_u": estimated_total_u - estimated_used_u,
            "usage_rate": round((estimated_used_u / estimated_total_u * 100) if estimated_total_u > 0 else 0, 2),
            "total_racks": 20,
            "devices_with_position": len(devices_with_position),
            "devices_without_position": len(devices) - len(devices_with_position)
        }
    except Exception as e:
        return {
            "total_u": 0,
            "used_u": 0,
            "available_u": 0,
            "usage_rate": 0,
            "error": str(e)
        }

# 兼容性端点 - 直接实现以避免函数名冲突
@app.get(f"{API_PREFIX}/devices/ports/statistics")
def get_device_ports_statistics_compat(db: Session = Depends(get_db)):
    """兼容性端点：设备端口统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])
        estimated_total_ports = total_devices * 24
        estimated_used_ports = active_devices * 12
        return {
            "total_ports": estimated_total_ports,
            "used_ports": estimated_used_ports,
            "usage_rate": round((estimated_used_ports / estimated_total_ports * 100) if estimated_total_ports > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {"total_ports": 0, "used_ports": 0, "usage_rate": 0, "error": str(e)}

@app.get(f"{API_PREFIX}/inspection-results/")
def get_inspection_results_compat(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """兼容性端点：巡检结果"""
    try:
        from app.models import Device
        from datetime import datetime, timedelta
        devices = db.query(Device).offset(skip).limit(limit).all()
        results = []
        for device in devices:
            last_check = device.last_check_time or (datetime.now() - timedelta(hours=1))
            results.append({
                "id": device.id,
                "device_id": device.id,
                "device_name": device.name,
                "ip_address": device.ip_address,
                "status": "online" if device.is_active else "offline",
                "check_time": last_check.isoformat() if last_check else None,
                "cpu_usage": device.cpu_usage or "N/A",
                "memory_usage": device.memory_usage or "N/A",
                "response_time": "10ms" if device.is_active else "timeout"
            })
        total = db.query(Device).count()
        return {"items": results, "total": total, "skip": skip, "limit": limit}
    except Exception as e:
        return {"items": [], "total": 0, "skip": skip, "limit": limit, "error": str(e)}

@app.get(f"{API_PREFIX}/statistics/ip-management-stats")
def get_ip_management_statistics_compat(db: Session = Depends(get_db)):
    """兼容性端点：IP管理统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        total_devices = len(devices)
        active_devices = len([d for d in devices if d.is_active])
        estimated_total_ips = 254 * 10
        estimated_used_ips = total_devices
        return {
            "total_ips": estimated_total_ips,
            "used_ips": estimated_used_ips,
            "available_ips": estimated_total_ips - estimated_used_ips,
            "usage_rate": round((estimated_used_ips / estimated_total_ips * 100) if estimated_total_ips > 0 else 0, 2),
            "total_devices": total_devices,
            "active_devices": active_devices
        }
    except Exception as e:
        return {"total_ips": 0, "used_ips": 0, "available_ips": 0, "usage_rate": 0, "error": str(e)}

@app.get(f"{API_PREFIX}/statistics/rack-management-stats")
def get_rack_management_statistics_compat(db: Session = Depends(get_db)):
    """兼容性端点：机柜管理统计"""
    try:
        from app.models import Device
        devices = db.query(Device).all()
        devices_with_position = [d for d in devices if d.rack and d.position]
        estimated_total_u = 42 * 20
        estimated_used_u = len(devices_with_position)
        return {
            "total_u": estimated_total_u,
            "used_u": estimated_used_u,
            "available_u": estimated_total_u - estimated_used_u,
            "usage_rate": round((estimated_used_u / estimated_total_u * 100) if estimated_total_u > 0 else 0, 2),
            "total_racks": 20,
            "devices_with_position": len(devices_with_position),
            "devices_without_position": len(devices) - len(devices_with_position)
        }
    except Exception as e:
        return {"total_u": 0, "used_u": 0, "available_u": 0, "usage_rate": 0, "error": str(e)}

# 添加重定向端点来处理原始路径
@app.get(f"{API_PREFIX}/ip-management/statistics")
def redirect_ip_management_statistics(db: Session = Depends(get_db)):
    """重定向端点：IP管理统计"""
    return get_ip_management_statistics_compat(db)

@app.get(f"{API_PREFIX}/rack-management/statistics")
def redirect_rack_management_statistics(db: Session = Depends(get_db)):
    """重定向端点：机柜管理统计"""
    return get_rack_management_statistics_compat(db)

@app.get(f"{API_PREFIX}/raw-devices")
def get_raw_devices():
    """直接访问设备数据，绕过所有中间件和路由器"""
    try:
        from app.database import SessionLocal
        from app.models import Device
        
        with SessionLocal() as db:
            devices = db.query(Device).all()
            
            # 转换为纯列表
            result = []
            for device in devices:
                result.append({
                    "id": device.id,
                    "name": device.name,
                    "ip_address": device.ip_address,
                    "device_type": device.device_type,
                    "manufacturer": device.manufacturer,
                    "model": device.model,
                    "is_active": device.is_active
                })
            
            return result
    except Exception as e:
        import traceback
        return {"error": str(e), "traceback": traceback.format_exc()}

@app.get(f"{API_PREFIX}/cli-devices")
def cli_devices():
    """模拟命令行获取设备，用于调试"""
    import sys
    import subprocess
    
    try:
        # 在Python中执行命令行脚本来查询数据库
        script = """
import os
import sys
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# 加载环境变量
DATABASE_URL = os.environ.get("DATABASE_URL")
if not DATABASE_URL:
    print("未设置数据库URL")
    sys.exit(1)

# 创建连接
engine = create_engine(DATABASE_URL)
Session = sessionmaker(bind=engine)
session = Session()

# 运行原始SQL
devices = []
try:
    result = session.execute("SELECT * FROM devices")
    for row in result:
        devices.append(dict(row))
except Exception as e:
    print(f"SQL错误: {e}")
    try:
        result = session.execute("SELECT * FROM device")
        for row in result:
            devices.append(dict(row))
        print("注意: 表名是'device'而不是'devices'")
    except Exception as e2:
        print(f"尝试使用device表名也失败: {e2}")

# 打印结果
print(f"找到 {len(devices)} 台设备")
for device in devices[:3]:  # 只打印前3个
    print(device)
        """
        
        # 保存到临时文件并执行
        import tempfile
        with tempfile.NamedTemporaryFile(suffix='.py', delete=False) as f:
            f.write(script.encode('utf-8'))
            temp_file = f.name
        
        # 执行脚本并捕获输出
        env = os.environ.copy()
        result = subprocess.run([sys.executable, temp_file], 
                               capture_output=True, text=True, env=env)
        os.unlink(temp_file)
        
        return {
            "stdout": result.stdout,
            "stderr": result.stderr,
            "return_code": result.returncode
        }
    except Exception as e:
        return {"error": str(e)}

# 注释掉全局路由处理器，因为它会拦截API请求
# @app.get("/{full_path:path}")
# async def serve_spa(full_path: str):
#     """SPA回退处理，处理前端路由"""
#     from fastapi.responses import FileResponse
#     from fastapi import HTTPException
#     import os
#
#     # 如果是API路径，跳过
#     if full_path.startswith("api/"):
#         raise HTTPException(status_code=404, detail="API路径未找到")
#
#     static_dir = "app/static/dist"
#
#     # 检查请求的文件是否存在
#     file_path = os.path.join(static_dir, full_path)
#     if os.path.isfile(file_path):
#         return FileResponse(file_path)
#
#     # 对于所有其他路径，返回index.html（SPA回退）
#     index_path = os.path.join(static_dir, "index.html")
#     if os.path.exists(index_path):
#         return FileResponse(index_path)
#
#     raise HTTPException(status_code=404, detail="页面未找到")

@app.post(f"{API_PREFIX}/rebuild-devices")
def rebuild_devices():
    """重建设备表并创建标准测试设备"""
    try:
        from app.database import engine, SessionLocal
        from app.models import Device, Base
        import random
        
        # 删除并重建设备表
        try:
            Device.__table__.drop(engine)
            Device.__table__.create(engine)
        except Exception as e:
            return {"status": "error", "message": f"重建表失败: {str(e)}"}
        
        # 创建多个新设备
        test_devices = []
        
        # 添加一些常见设备类型
        device_templates = [
            {"prefix": "Router", "type": "router", "manufacturer": "cisco", "model_prefix": "CSR"},
            {"prefix": "Switch", "type": "switch", "manufacturer": "huawei", "model_prefix": "S"},
            {"prefix": "Firewall", "type": "firewall", "manufacturer": "fortinet", "model_prefix": "FG"},
            {"prefix": "AP", "type": "wireless", "manufacturer": "aruba", "model_prefix": "AP"}
        ]
        
        with SessionLocal() as db:
            for i in range(1, 21):  # 创建20台设备
                template = random.choice(device_templates)
                device = Device(
                    name=f"{template['prefix']}-{i:02d}",
                    ip_address=f"192.168.{random.randint(1, 254)}.{random.randint(1, 254)}",
                    device_type=template["type"],
                    manufacturer=template["manufacturer"],
                    model=f"{template['model_prefix']}{random.randint(100, 999)}",
                    username="admin",
                    password=f"Password{i}",
                    is_active=random.choice([True, False])
                )
                db.add(device)
                test_devices.append(device.name)
            
            db.commit()
            
            # 验证
            count = db.query(Device).count()
            return {
                "status": "success",
                "message": f"成功重建设备表并创建 {count} 台设备",
                "devices": test_devices
            }
            
    except Exception as e:
        import traceback
        return {
            "status": "error",
            "message": f"重建设备表失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.post(f"{API_PREFIX}/devices/quick-refresh")
def quick_refresh_devices(db: Session = Depends(get_db)):
    """快速刷新设备状态（随机模拟在线离线状态变化）"""
    try:
        from app.models import Device
        import random
        
        # 获取所有设备
        devices = db.query(Device).all()
        if not devices:
            return {"status": "warning", "message": "没有设备可刷新", "updated": 0}
        
        # 随机更新部分设备的状态
        updated_count = 0
        for device in devices:
            # 20%的概率改变状态
            if random.random() < 0.2:
                device.is_active = not device.is_active
                updated_count += 1
        
        # 提交更改
        db.commit()
        
        return {
            "status": "success", 
            "message": f"已快速刷新 {len(devices)} 台设备状态，{updated_count} 台状态变化",
            "updated": updated_count,
            "total": len(devices)
        }
    except Exception as e:
        return {"status": "error", "message": f"快速刷新失败: {str(e)}"}

@app.get(f"{API_PREFIX}/debug-pagination")
def debug_pagination(
    page: int = Query(1, ge=1),
    pageSize: int = Query(10, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """用于调试分页的专用端点"""
    try:
        from app.models import Device
        
        # 查询总记录数
        total = db.query(Device).count()
        
        # 所有设备ID和名称
        all_devices = db.query(Device.id, Device.name).all()
        all_ids = [d.id for d in all_devices]
        
        # 计算偏移量
        skip = (page - 1) * pageSize
        
        # 查询分页数据
        devices = db.query(Device).offset(skip).limit(pageSize).all()
        page_ids = [d.id for d in devices]
        
        return {
            "debug_info": {
                "request": {
                    "page": page,
                    "pageSize": pageSize,
                    "calculated_skip": skip
                },
                "database": {
                    "total_count": total,
                    "all_devices_count": len(all_devices),
                    "returned_count": len(devices)
                },
                "data_details": {
                    "all_ids": all_ids,
                    "page_ids": page_ids,
                    "page_range": f"{skip+1}-{min(skip+pageSize, total)}/{total}"
                }
            },
            "data": [
                {
                    "id": device.id,
                    "name": device.name,
                    "ip_address": device.ip_address
                } for device in devices
            ],
            "pagination": {
                "current": page,
                "pageSize": pageSize,
                "total": total,
                "totalPages": (total + pageSize - 1) // pageSize
            }
        }
    except Exception as e:
        logger.error(f"调试分页失败: {str(e)}")
        import traceback
        return {
            "status": "error", 
            "message": f"调试分页失败: {str(e)}",
            "traceback": traceback.format_exc()
        }

@app.get("/terminal")
async def get_terminal():
    # 重定向到静态终端页面
    static_dir = "app/static/dist"
    return FileResponse(os.path.join(static_dir, "terminal.html"))

# 现代方式设置路由优先级
from fastapi.routing import APIRoute

# 为单个路由设置优先级
@app.get("/example", response_model=None)
def example():
    return {"message": "example"}

# 在添加路由时设置其优先级
app.routes[-1].priority = 1

@app.get("/api/direct-ip-test")
def direct_ip_test():
    """直接测试IP管理相关功能"""
    return {
        "status": "ok",
        "message": "直接路由测试成功",
        "timestamp": str(datetime.datetime.now())
    }

# 添加直接的IP管理测试路由
@app.get("/api/ip-test")
def ip_test():
    return {
        "status": "ok",
        "message": "IP管理测试路由响应正常",
        "timestamp": str(datetime.datetime.now())
    }

# 添加直接的子网测试路由
@app.get("/api/subnets-direct")
def subnets_direct():
    from app.database import SessionLocal
    try:
        from app.models import Subnet
        
        with SessionLocal() as db:
            # 检查子网表是否存在
            try:
                subnets = db.query(Subnet).all()
                subnet_data = [{"id": s.id, "name": s.name, "cidr": s.cidr} for s in subnets]
                return subnet_data
            except Exception as e:
                return {"error": f"查询子网失败: {str(e)}"}
    except ImportError:
        return {"error": "无法导入Subnet模型，可能未定义"}
    except Exception as e:
        return {"error": str(e)}

@app.get(f"{API_PREFIX}/api-health")
def api_health_check():
    """API健康检查端点，检查所有已注册的路由"""
    routes = []
    for route in app.routes:
        if hasattr(route, "path") and hasattr(route, "methods"):
            routes.append({
                "path": route.path,
                "methods": list(route.methods)
            })
        
    # 对 routes 按照路径排序
    routes.sort(key=lambda x: x["path"])
    
    return {
        "status": "ok",
        "timestamp": datetime.datetime.now().isoformat(),
        "routes_count": len(routes),
        "registered_prefixes": [
            "/api/devices",
            "/api/config",
            API_PREFIX
        ],
        "routes_sample": routes[:10]  # 只返回前10个路由示例
    }

# AI Assistant API proxy routes
@app.post("/api/ai/settings")
async def ai_settings_proxy(request: fastapi.Request):
    """
    Proxy for AI settings API
    """
    try:
        # Get request body
        body = await request.json()
        
        # Forward request to backend AI API
        import requests
        response = requests.post(
            "http://localhost:5888/backend/api/ai/settings", 
            json=body,
            headers={"Content-Type": "application/json"}
        )
        
        # Return response from backend
        return response.json()
    except Exception as e:
        logger.error(f"Error proxying AI settings request: {str(e)}")
        return {
            "success": False,
            "message": f"Error processing AI settings request: {str(e)}"
        }

@app.get("/api/ai/settings")
async def ai_settings_get_proxy():
    """
    Proxy for getting AI settings
    """
    try:
        # Forward request to backend AI API
        import requests
        response = requests.get("http://localhost:5888/backend/api/ai/settings")
        
        # Return response from backend
        return response.json()
    except Exception as e:
        logger.error(f"Error getting AI settings: {str(e)}")
        return {
            "success": False,
            "message": f"Error getting AI settings: {str(e)}",
            "endpoint": "",
            "provider": "custom"
        }

@app.post("/api/ai/chat")
async def ai_chat_proxy(request: fastapi.Request):
    """
    Proxy for AI chat API
    """
    try:
        # Get request body
        body = await request.json()
        
        # Forward request to backend AI API
        import requests
        response = requests.post(
            "http://localhost:5888/backend/api/ai/chat", 
            json=body,
            headers={"Content-Type": "application/json"}
        )
        
        # Return response from backend
        return response.json()
    except Exception as e:
        logger.error(f"Error proxying AI chat request: {str(e)}")
        return {
            "success": False,
            "message": f"Error processing AI chat request: {str(e)}"
        }

@app.get("/api/ai-webssh-test")
async def ai_webssh_test():
    """测试WebSSH AI路由是否工作"""
    return {"status": "ok", "message": "WebSSH AI路由测试成功"}

@app.get("/api/webssh-debug")
async def webssh_debug():
    """WebSSH诊断端点，用于调试WebSSH连接问题"""
    from .webssh.manager import session_manager
    
    # 获取所有注册的WebSocket路由
    websocket_routes = []
    for route in app.routes:
        if hasattr(route, "path") and "ws" in route.path.lower():
            websocket_routes.append({
                "path": route.path,
                "name": getattr(route, "name", "unnamed"),
                "methods": list(getattr(route, "methods", [])) if hasattr(route, "methods") else ["WebSocket"]
            })
    
    # 获取当前活动的WebSSH会话
    active_sessions = []
    for session_id, session in session_manager.sessions.items():
        info = session["info"].copy() if "info" in session else {}
        if "password" in info:
            info["password"] = "********"  # 隐藏密码
        
        active_sessions.append({
            "session_id": session_id,
            "created_at": session.get("created_at"),
            "last_accessed": session.get("last_accessed"),
            "info": info
        })
    
    # 获取WebSSH相关的路由
    webssh_routes = []
    for route in app.routes:
        if hasattr(route, "path") and ("/terminal/" in route.path or "/webssh" in route.path):
            webssh_routes.append({
                "path": route.path,
                "methods": list(getattr(route, "methods", [])) if hasattr(route, "methods") else ["GET"],
                "name": getattr(route, "name", "unnamed")
            })
    
    # Define static_dir for this endpoint
    static_dir = "app/static/dist"

    return {
        "status": "ok",
        "websocket_routes": websocket_routes,
        "webssh_routes": webssh_routes,
        "active_sessions": active_sessions,
        "session_count": len(session_manager.sessions),
        "static_files": {
            "terminal_html": os.path.exists(os.path.join(static_dir, "terminal.html")),
            "webssh_html": os.path.exists(os.path.join(os.path.dirname(os.path.abspath(__file__)), "static/webssh.html"))
        }
    }

@app.post("/api/test-post")
def test_post():
    """测试POST方法是否工作"""
    print("收到POST请求至测试端点")
    return {"status": "ok", "message": "POST请求测试成功"}

# Add these endpoints after the API root endpoint
@app.get("/api/config/debug/test")
def config_debug_test():
    """Debug test endpoint for configuration API connectivity"""
    return {
        "status": "success",
        "message": "Configuration API debug endpoint is accessible",
        "timestamp": datetime.datetime.now().isoformat()
    }

@app.get("/api/config/debug/ports")
def config_debug_ports():
    """Debug endpoint that returns sample port data"""
    import random
    
    # Generate a random number of ports
    num_ports = 16
    ports = []
    
    for i in range(1, num_ports + 1):
        # Randomize port status
        status_options = ['up', 'down', 'shutdown']
        status_weights = [0.3, 0.6, 0.1]  # 30% up, 60% down, 10% shutdown
        status = random.choices(status_options, weights=status_weights)[0]
        
        # Only set vlan and description for 'up' ports
        vlan = str(random.randint(1, 100)) if status == 'up' else ''
        description = f"Debug Port {i}" if status == 'up' else ''
        
        port = {
            "id": i,
            "device_id": "debug-device",
            "device_name": "Debug Device",
            "port_number": i,
            "slot": 0,
            "port_type": "业务端口",
            "status": status,
            "vlan": vlan,
            "description": description,
            "config": {},
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat()
        }
        ports.append(port)
    
    return ports

@app.get("/api/config/ports/device/{device_id}/fetch/debug")
def fetch_device_ports_debug(device_id: str):
    """Debug endpoint that simulates fetching device ports"""
    import random
    
    # Generate a random number of ports between 16 and 48
    num_ports = random.randint(16, 48)
    ports = []
    
    for i in range(1, num_ports + 1):
        # Randomize port status
        status_options = ['up', 'down', 'shutdown']
        status_weights = [0.3, 0.6, 0.1]  # 30% up, 60% down, 10% shutdown
        status = random.choices(status_options, weights=status_weights)[0]
        
        # Only set vlan and description for 'up' ports
        vlan = str(random.randint(1, 100)) if status == 'up' else ''
        description = f"Port {i}" if status == 'up' else ''
        
        # Assign slots - first 24 in slot 0, next 24 in slot 1, etc.
        slot = (i - 1) // 24
        
        port = {
            "id": i,
            "device_id": device_id,
            "device_name": f"Device {device_id}",
            "port_number": i,
            "slot": slot,
            "port_type": "业务端口",
            "status": status,
            "vlan": vlan,
            "description": description,
            "config": {},
            "created_at": datetime.datetime.now().isoformat(),
            "updated_at": datetime.datetime.now().isoformat()
        }
        ports.append(port)
    
    return ports

@app.get(f"{API_PREFIX}/device-groups")
def get_device_groups(db: Session = Depends(get_db)):
    """直接路径的设备组API"""
    try:
        # 调用拓扑路由器中的方法
        from app.routers.device_groups import get_device_groups as dg_func
        return dg_func(db)
    except Exception as e:
        logger.error(f"获取设备组失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.get(f"{API_PREFIX}/topology")
def get_topology(db: Session = Depends(get_db)):
    """直接路径的拓扑API"""
    try:
        # 调用拓扑路由器中的方法
        from app.routers.topology import get_topology_data as td_func
        return td_func(db=db)
    except Exception as e:
        logger.error(f"获取拓扑数据失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.post(f"{API_PREFIX}/topology/discovery")
async def start_discovery(request: Request, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """直接路径的拓扑发现API"""
    try:
        # 从请求体获取数据
        body = await request.json()
        
        # 调用拓扑路由器中的方法
        from app.routers.topology import start_topology_discovery as std_func
        from app.schemas.topology import DiscoveryConfig
        
        # 创建配置对象
        config = DiscoveryConfig(**body)
        
        # 调用发现函数
        return std_func(config=config, background_tasks=background_tasks, db=db)
    except Exception as e:
        logger.error(f"启动拓扑发现失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.get(f"{API_PREFIX}/topology/discovery/{{task_id}}")
def get_discovery_progress(task_id: str):
    """获取拓扑发现进度API"""
    try:
        # 调用拓扑路由器中的方法
        from app.routers.topology import get_discovery_progress as gdp_func
        return gdp_func(task_id=task_id)
    except Exception as e:
        logger.error(f"获取拓扑发现进度失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.post(f"{API_PREFIX}/topology/discovery/{{task_id}}/cancel")
def cancel_topology_discovery(task_id: str):
    """取消拓扑发现任务API"""
    try:
        # 调用拓扑路由器中的方法
        from app.routers.topology import cancel_discovery as cd_func
        return cd_func(task_id=task_id)
    except Exception as e:
        logger.error(f"取消拓扑发现任务失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

@app.post(f"{API_PREFIX}/topology/update-lldp")
async def update_topology_with_lldp(request: Request, background_tasks: BackgroundTasks, db: Session = Depends(get_db)):
    """通过LLDP更新拓扑API"""
    try:
        # 从请求体获取数据
        body = await request.json()
        
        # 调用拓扑路由器中的方法
        from app.routers.topology import update_topology_with_lldp as utl_func
        from app.schemas.topology import LLDPUpdateRequest
        
        # 创建请求对象
        lldp_request = LLDPUpdateRequest(**body)
        
        # 调用LLDP更新函数
        return await utl_func(request=lldp_request, background_tasks=background_tasks, db=db)
    except Exception as e:
        logger.error(f"LLDP拓扑更新失败: {str(e)}", exc_info=True)
        return {"error": str(e)}

if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=5888, reload=True)