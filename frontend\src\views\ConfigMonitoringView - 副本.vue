<template>
  <div class="config-monitoring-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2>配置监控</h2>
          <div class="header-actions">
            <el-button type="primary" @click="refreshDevices" :loading="loading">
              <el-icon><Refresh /></el-icon> 刷新设备
            </el-button>
            <el-button type="success" @click="startBatchMonitoring" :disabled="selectedDevices.length === 0">
              🔍 批量监控 ({{ selectedDevices.length }}个设备)
            </el-button>
            <el-button type="warning" @click="stopBatchMonitoring" :disabled="!isMonitoring">
              ⏹️ 停止监控
            </el-button>
            <el-button type="info" @click="viewChangeHistory">
              📋 查看变更记录
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索筛选区域 -->
      <div class="filter-section">
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item>
            <el-input 
              v-model="filterForm.name" 
              placeholder="设备名称" 
              clearable
              prefix-icon="Search"
              @input="handleFilter"
            />
          </el-form-item>
          <el-form-item>
            <el-input 
              v-model="filterForm.ip" 
              placeholder="IP地址" 
              clearable
              prefix-icon="Location"
              @input="handleFilter"
            />
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterForm.type" placeholder="设备类型" clearable @change="handleFilter" style="width: 140px;">
              <el-option label="路由器" value="router" />
              <el-option label="交换机" value="switch" />
              <el-option label="防火墙" value="firewall" />
              <el-option label="服务器" value="server" />
              <el-option label="台式机" value="desktop" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterForm.monitoring_status" placeholder="监控状态" clearable @change="handleFilter" style="width: 140px;">
              <el-option label="监控中" value="monitoring" />
              <el-option label="已停止" value="stopped" />
              <el-option label="未开始" value="not_started" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select v-model="filterForm.online_status" placeholder="在线状态" clearable @change="handleFilter" style="width: 140px;">
              <el-option label="在线" value="online" />
              <el-option label="离线" value="offline" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="resetFilter">
              <el-icon><Refresh /></el-icon> 重置
            </el-button>
            <el-button
              type="info"
              size="small"
              @click="showHelp = !showHelp"
              :icon="showHelp ? 'ArrowUp' : 'ArrowDown'"
              style="margin-left: 10px;"
            >
              {{ showHelp ? '隐藏说明' : '使用说明' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 现代化帮助说明内容 -->
      <div class="help-section-modern">
        <el-collapse-transition>
          <div v-show="showHelp" class="help-content-modern">
            <div class="help-header">
              <span class="help-icon">ℹ️</span>
              <h4>使用说明</h4>
            </div>
            <div class="help-items">
              <div class="help-item">
                <span class="item-icon">🔍</span>
                <span>配置监控功能定期检查设备配置变化，发现变更时自动提醒</span>
              </div>
              <div class="help-item">
                <span class="item-icon">⏰</span>
                <span>监控间隔可设置为10秒、30秒、1分钟、5分钟、10分钟、30分钟或1小时</span>
              </div>
              <div class="help-item highlight">
                <span class="item-icon">⚠️</span>
                <span><strong>只有在线设备才能进行配置监控</strong>，离线设备将被自动跳过</span>
              </div>
              <div class="help-item">
                <span class="item-icon">📊</span>
                <span>系统会记录所有配置变更历史，支持查看详细的变更内容</span>
              </div>
              <div class="help-item">
                <span class="item-icon">🔔</span>
                <span>发现配置变更时会在界面显示通知，并记录到变更日志中</span>
              </div>
            </div>
          </div>
        </el-collapse-transition>
      </div>

      <!-- 统计信息 -->
      <div class="stats-section">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-statistic title="总设备数" :value="devices.length" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="在线设备" :value="onlineDevices.length" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="离线设备" :value="offlineDevices.length" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="监控中" :value="monitoringCount" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="配置变更" :value="configChanges.length" />
          </el-col>
          <el-col :span="4">
            <el-statistic title="监控失败" :value="failedCount" />
          </el-col>
        </el-row>
      </div>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="paginatedDevices"
        stripe
        border
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :row-class-name="getRowClassName"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" width="80" align="center">
          <template #default="scope">
            {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="设备名称" min-width="120" show-overflow-tooltip align="center" />
        <el-table-column prop="ip_address" label="IP地址" width="140" align="center" />
        <el-table-column prop="device_type" label="设备类型" width="100" align="center">
          <template #default="scope">
            {{ getDeviceTypeLabel(scope.row.device_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="manufacturer" label="厂商" width="100" align="center">
          <template #default="scope">
            {{ getManufacturerLabel(scope.row.manufacturer) }}
          </template>
        </el-table-column>
        <el-table-column label="在线状态" width="100" align="center">
          <template #default="scope">
            <div class="status-cell">
              <el-tag
                :type="scope.row.is_active ? 'success' : 'danger'"
                size="small"
              >
                <el-icon v-if="scope.row.is_active"><CircleCheck /></el-icon>
                <el-icon v-else><CircleClose /></el-icon>
                {{ scope.row.is_active ? '在线' : '离线' }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="监控状态" width="120" align="center">
          <template #default="scope">
            <div class="monitoring-status-cell">
              <el-tag
                :type="getMonitoringStatusType(scope.row.monitoring_status)"
                size="small"
              >
                <el-icon v-if="scope.row.monitoring_status === 'monitoring'" class="is-loading"><Loading /></el-icon>
                {{ getMonitoringStatusText(scope.row.monitoring_status) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="监控间隔" width="100" align="center">
          <template #default="scope">
            <span v-if="scope.row.monitoring_interval">
              {{ formatInterval(scope.row.monitoring_interval) }}
            </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="最后检查" width="160" align="center">
          <template #default="scope">
            {{ scope.row.last_check_time ? formatDate(scope.row.last_check_time) : '-' }}
          </template>
        </el-table-column>
        <el-table-column label="配置变更" width="120" align="center">
          <template #default="scope">
            <div class="config-changes-cell">
              <el-tag v-if="getDeviceChangesCount(scope.row.id) > 0" type="warning" size="small">
                {{ getDeviceChangesCount(scope.row.id) }}次变更
              </el-tag>
              <span v-else class="no-changes">无变更</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right" align="center">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                size="small"
                type="primary"
                @click="startMonitoring(scope.row)"
                :loading="scope.row.monitoring_status === 'starting'"
                :disabled="!scope.row.is_active || scope.row.monitoring_status === 'monitoring'"
                class="monitor-btn"
              >
                🔍 {{ scope.row.monitoring_status === 'monitoring' ? '监控中' : '开始' }}
              </el-button>
              <div v-if="!scope.row.is_active" class="offline-tip">
                <el-text size="small" type="info">设备离线</el-text>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-section">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredDevices.length"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 监控设置对话框 -->
    <el-dialog
      v-model="monitoringDialogVisible"
      title="监控设置"
      width="500px"
    >
      <div class="monitoring-settings">
        <el-form :model="monitoringForm" label-width="120px">
          <el-form-item label="设备名称:">
            <el-input v-model="monitoringForm.deviceName" disabled />
          </el-form-item>
          <el-form-item label="监控间隔:">
            <el-select v-model="monitoringForm.interval" placeholder="请选择监控间隔">
              <el-option label="10秒" :value="0.17" />
              <el-option label="30秒" :value="0.5" />
              <el-option label="1分钟" :value="1" />
              <el-option label="5分钟" :value="5" />
              <el-option label="10分钟" :value="10" />
              <el-option label="30分钟" :value="30" />
              <el-option label="1小时" :value="60" />
            </el-select>
          </el-form-item>
          <el-form-item label="变更通知:">
            <el-switch v-model="monitoringForm.enableNotification" />
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="monitoringDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmStartMonitoring">开始监控</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 配置变更详情对话框 -->
    <el-dialog
      v-model="changesDialogVisible"
      title="配置变更记录"
      width="80%"
      top="5vh"
    >
      <div class="changes-content">
        <div class="changes-header">
          <h4>{{ selectedDevice?.name }} - 配置变更记录</h4>
          <el-button type="primary" size="small" @click="refreshChanges">
            <el-icon><Refresh /></el-icon> 刷新
          </el-button>
        </div>
        
        <el-table
          :data="deviceChanges"
          stripe
          border
          max-height="400"
        >
          <el-table-column label="变更时间" width="180">
            <template #default="scope">
              {{ formatDate(scope.row.change_time) }}
            </template>
          </el-table-column>
          <el-table-column label="变更类型" width="120">
            <template #default="scope">
              <el-tag :type="getChangeTypeTag(scope.row.change_type)" size="small">
                {{ getChangeTypeText(scope.row.change_type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="变更描述" min-width="200" show-overflow-tooltip />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-button size="small" type="info" @click="viewChangeDetail(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="changesDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量监控进度对话框 -->
    <el-dialog
      v-model="batchDialogVisible"
      title="批量监控设置"
      width="600px"
    >
      <div class="batch-progress">
        <el-progress :percentage="batchProgress" :status="batchStatus" />
        <p class="progress-text">{{ batchProgressText }}</p>
      </div>
      
      <div class="batch-results" v-if="batchResults.length > 0">
        <el-table :data="batchResults" max-height="300">
          <el-table-column prop="name" label="设备名称" width="150" />
          <el-table-column prop="ip_address" label="IP地址" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status === 'success' ? 'success' : 'danger'" size="small">
                {{ scope.row.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="message" label="说明" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  Refresh, Download, Search, Loading, CircleCheck, CircleClose
} from '@element-plus/icons-vue'
import axios from 'axios'

// 响应式数据
const loading = ref(false)
const devices = ref([])
const configChanges = ref([])
const selectedDevices = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const showHelp = ref(false)

// 筛选表单
const filterForm = ref({
  name: '',
  ip: '',
  type: '',
  monitoring_status: '',
  online_status: ''
})

// 监控设置相关
const monitoringDialogVisible = ref(false)
const monitoringForm = ref({
  deviceId: null,
  deviceName: '',
  interval: 10,
  enableNotification: true
})

// 配置变更相关
const changesDialogVisible = ref(false)
const selectedDevice = ref(null)
const deviceChanges = ref([])

// 批量监控相关
const batchDialogVisible = ref(false)
const batchProgress = ref(0)
const batchStatus = ref('')
const batchProgressText = ref('')
const batchResults = ref([])

// 监控定时器
const monitoringTimers = ref(new Map())

// 后台运行相关
const STORAGE_KEY = 'config_monitoring_state'

// 保存监控状态到localStorage
const saveMonitoringState = () => {
  const monitoringDevices = devices.value.filter(device => device.monitoring_status === 'monitoring')
  const state = {
    devices: monitoringDevices.map(device => ({
      id: device.id,
      name: device.name,
      ip_address: device.ip_address,
      monitoring_interval: device.monitoring_interval,
      monitoring_status: device.monitoring_status,
      last_check_time: device.last_check_time
    })),
    timestamp: Date.now()
  }
  localStorage.setItem(STORAGE_KEY, JSON.stringify(state))
}

// 从localStorage恢复监控状态
const restoreMonitoringState = () => {
  try {
    const savedState = localStorage.getItem(STORAGE_KEY)
    if (!savedState) return
    
    const state = JSON.parse(savedState)
    const now = Date.now()
    
    // 检查状态是否过期（超过24小时则清除）
    if (now - state.timestamp > 24 * 60 * 60 * 1000) {
      localStorage.removeItem(STORAGE_KEY)
      return
    }
    
    // 恢复监控状态
    state.devices.forEach(savedDevice => {
      const device = devices.value.find(d => d.id === savedDevice.id)
      if (device && device.is_active) {
        device.monitoring_status = 'monitoring'
        device.monitoring_interval = savedDevice.monitoring_interval
        device.last_check_time = savedDevice.last_check_time
        
        // 重新启动监控
        startDeviceMonitoring(device)
      }
    })
    
    if (state.devices.length > 0) {
      ElMessage.success(`已恢复 ${state.devices.length} 个设备的监控状态`)
    }
  } catch (error) {
    console.error('恢复监控状态失败:', error)
    localStorage.removeItem(STORAGE_KEY)
  }
}

// 设备类型映射
const deviceTypeMap = {
  'router': '路由器',
  'switch': '交换机', 
  'firewall': '防火墙',
  'server': '服务器',
  'desktop': '台式机',
  'laptop': '笔记本',
  'printer': '打印机',
  'loadbalancer': '负载均衡',
  'ac_controller': 'AC控制器',
  'access_point': 'AP'
}

// 厂商映射
const manufacturerMap = {
  'huawei': '华为',
  'cisco': '思科',
  'h3c': '华三',
  'ruijie': '锐捷',
  'zte': '中兴',
  'other': '其他'
}

// 计算属性
const filteredDevices = computed(() => {
  let filtered = devices.value

  if (filterForm.value.name) {
    filtered = filtered.filter(device =>
      device.name.toLowerCase().includes(filterForm.value.name.toLowerCase())
    )
  }

  if (filterForm.value.ip) {
    filtered = filtered.filter(device =>
      device.ip_address.includes(filterForm.value.ip)
    )
  }

  if (filterForm.value.type) {
    filtered = filtered.filter(device =>
      device.device_type === filterForm.value.type
    )
  }

  if (filterForm.value.monitoring_status) {
    filtered = filtered.filter(device => {
      const status = device.monitoring_status || 'not_started'
      return status === filterForm.value.monitoring_status
    })
  }

  if (filterForm.value.online_status) {
    filtered = filtered.filter(device => {
      if (filterForm.value.online_status === 'online') {
        return device.is_active
      } else if (filterForm.value.online_status === 'offline') {
        return !device.is_active
      }
      return true
    })
  }

  // 确保在线设备始终排在前面
  return filtered.sort((a, b) => {
    if (a.is_active && !b.is_active) return -1
    if (!a.is_active && b.is_active) return 1
    return a.name.localeCompare(b.name)
  })
})

const onlineDevices = computed(() => {
  return devices.value.filter(device => device.is_active)
})

const offlineDevices = computed(() => {
  return devices.value.filter(device => !device.is_active)
})

const failedCount = computed(() => {
  return devices.value.filter(device => device.monitoring_status === 'failed').length
})

const monitoringCount = computed(() => {
  return devices.value.filter(device => device.monitoring_status === 'monitoring').length
})

const todayChanges = computed(() => {
  const today = new Date().toDateString()
  return configChanges.value.filter(change => {
    const changeDate = new Date(change.change_time).toDateString()
    return changeDate === today
  }).length
})

const monitoringCoverage = computed(() => {
  if (onlineDevices.value.length === 0) return 0
  return Math.round((monitoringCount.value / onlineDevices.value.length) * 100)
})

const hasActiveMonitoring = computed(() => {
  return monitoringCount.value > 0
})

// 分页后的设备数据
const paginatedDevices = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredDevices.value.slice(start, end)
})

// 方法
const getDeviceTypeLabel = (type) => {
  return deviceTypeMap[type] || type
}

const getManufacturerLabel = (manufacturer) => {
  return manufacturerMap[manufacturer] || manufacturer
}

const getMonitoringStatusType = (status) => {
  switch (status) {
    case 'monitoring': return 'success'
    case 'stopped': return 'warning'
    default: return 'info'
  }
}

const getMonitoringStatusText = (status) => {
  switch (status) {
    case 'monitoring': return '监控中'
    case 'stopped': return '已停止'
    default: return '未开始'
  }
}

const getChangeTypeTag = (type) => {
  switch (type) {
    case 'added': return 'success'
    case 'modified': return 'warning'
    case 'deleted': return 'danger'
    default: return 'info'
  }
}

const getChangeTypeText = (type) => {
  switch (type) {
    case 'added': return '新增'
    case 'modified': return '修改'
    case 'deleted': return '删除'
    default: return '其他'
  }
}

const formatDate = (dateString) => {
  if (!dateString) return '-'
  return new Date(dateString).toLocaleString('zh-CN')
}

const getDeviceChangesCount = (deviceId) => {
  return configChanges.value.filter(change => change.device_id === deviceId).length
}

const getRowClassName = ({ row }) => {
  if (!row.is_active) return 'offline-row'
  if (row.monitoring_status === 'monitoring') return 'monitoring-row'
  return ''
}

// 格式化时间间隔显示
const formatInterval = (interval) => {
  if (interval < 1) {
    const seconds = Math.round(interval * 60)
    return `${seconds}秒`
  } else if (interval < 60) {
    return `${interval}分钟`
  } else {
    const hours = Math.round(interval / 60)
    return `${hours}小时`
  }
}

const isMonitoring = computed(() => {
  return devices.value.some(device => device.monitoring_status === 'monitoring')
})

const stopBatchMonitoring = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要停止所有设备的监控吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      // 停止所有定时器
      monitoringTimers.value.forEach((timer, deviceId) => {
        clearInterval(timer)
      })
      monitoringTimers.value.clear()

      // 更新所有设备状态
      devices.value.forEach(device => {
        if (device.monitoring_status === 'monitoring') {
          device.monitoring_status = 'stopped'
          device.last_check_time = new Date().toISOString()
        }
      })
      
      // 保存监控状态到localStorage
      saveMonitoringState()

      ElMessage.success('已停止所有设备监控')
    }
  } catch (error) {
    // 用户取消操作
  }
}

const viewChangeHistory = () => {
  changesDialogVisible.value = true
  deviceChanges.value = configChanges.value
}

// 获取设备列表
const refreshDevices = async () => {
  loading.value = true
  try {
    let allDevices = []

    // 尝试获取设备列表
    try {
      const response = await axios.get('/api/simple-devices')
      if (Array.isArray(response.data)) {
        allDevices = response.data
      }
    } catch (error) {
      console.warn('获取设备列表失败:', error)
      // 使用模拟数据
      allDevices = [
        {
          id: 1,
          name: '核心交换机-01',
          ip_address: '***********',
          device_type: 'switch',
          manufacturer: 'huawei',
          is_active: true,
          monitoring_status: 'not_started',
          monitoring_interval: null,
          last_check_time: null
        },
        {
          id: 2,
          name: '边界路由器-01',
          ip_address: '***********',
          device_type: 'router',
          manufacturer: 'cisco',
          is_active: true,
          monitoring_status: 'monitoring',
          monitoring_interval: 10,
          last_check_time: new Date().toISOString()
        },
        {
          id: 3,
          name: '防火墙-01',
          ip_address: '***********',
          device_type: 'firewall',
          manufacturer: 'h3c',
          is_active: false,
          monitoring_status: 'stopped',
          monitoring_interval: 30,
          last_check_time: null
        }
      ]
    }

    devices.value = allDevices.map(device => ({
      ...device,
      monitoring_status: device.monitoring_status || 'not_started',
      monitoring_interval: device.monitoring_interval || null,
      last_check_time: device.last_check_time || null
    }))

    // 获取配置变更记录
    await loadConfigChanges()

    ElMessage.success(`成功加载 ${devices.value.length} 个设备`)
  } catch (error) {
    console.error('刷新设备失败:', error)
    ElMessage.error('刷新设备失败: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加载配置变更记录
const loadConfigChanges = async () => {
  try {
    // 模拟配置变更数据
    configChanges.value = [
      {
        id: 1,
        device_id: 2,
        change_time: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        change_type: 'modified',
        description: 'VLAN配置修改',
        details: 'VLAN 100 描述从 "Test VLAN" 修改为 "Production VLAN"'
      },
      {
        id: 2,
        device_id: 2,
        change_time: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
        change_type: 'added',
        description: '新增ACL规则',
        details: '新增ACL规则 permit tcp any host ************* eq 80'
      }
    ]
  } catch (error) {
    console.error('加载配置变更记录失败:', error)
  }
}

// 开始监控单个设备
const startMonitoring = (device) => {
  monitoringForm.value = {
    deviceId: device.id,
    deviceName: device.name,
    interval: 10,
    enableNotification: true
  }
  monitoringDialogVisible.value = true
}

// 确认开始监控
const confirmStartMonitoring = async () => {
  try {
    const device = devices.value.find(d => d.id === monitoringForm.value.deviceId)
    if (!device) return

    // 更新设备监控状态
    device.monitoring_status = 'monitoring'
    device.monitoring_interval = monitoringForm.value.interval
    device.last_check_time = new Date().toISOString()

    // 启动定时监控
    startDeviceMonitoring(device)
    
    // 保存监控状态到localStorage
    saveMonitoringState()

    ElMessage.success(`已开始监控设备: ${device.name}`)
    monitoringDialogVisible.value = false
  } catch (error) {
    console.error('开始监控失败:', error)
    ElMessage.error('开始监控失败: ' + error.message)
  }
}

// 停止监控单个设备
const stopMonitoring = async (device) => {
  try {
    // 停止定时器
    if (monitoringTimers.value.has(device.id)) {
      clearInterval(monitoringTimers.value.get(device.id))
      monitoringTimers.value.delete(device.id)
    }

    // 更新设备状态
    device.monitoring_status = 'stopped'
    device.last_check_time = new Date().toISOString()
    
    // 保存监控状态到localStorage
    saveMonitoringState()

    ElMessage.success(`已停止监控设备: ${device.name}`)
  } catch (error) {
    console.error('停止监控失败:', error)
    ElMessage.error('停止监控失败: ' + error.message)
  }
}

// 启动设备监控定时器
const startDeviceMonitoring = (device) => {
  // 清除已存在的定时器
  if (monitoringTimers.value.has(device.id)) {
    clearInterval(monitoringTimers.value.get(device.id))
  }

  // 计算间隔时间（毫秒）
  let intervalMs
  if (device.monitoring_interval < 1) {
    // 小于1分钟的按秒计算
    intervalMs = device.monitoring_interval * 60 * 1000
  } else {
    // 大于等于1分钟的按分钟计算
    intervalMs = device.monitoring_interval * 60 * 1000
  }

  // 创建新的定时器
  const timer = setInterval(async () => {
    await checkDeviceConfig(device)
  }, intervalMs)

  monitoringTimers.value.set(device.id, timer)

  // 立即执行一次检查
  checkDeviceConfig(device)
}

// 检查设备配置
const checkDeviceConfig = async (device) => {
  try {
    console.log(`检查设备配置: ${device.name}`)
    
    // 调用后端API检测配置变更
    const response = await axios.post('/api/config-monitoring/check_config_changes', {
      device_id: device.id,
      device_info: {
        username: device.username || 'admin',
        password: device.password || 'admin',
        port: device.port || 22,
        timeout: 30
      }
    })
    
    const result = response.data
    
    if (result.status === 'success') {
      if (result.has_changes && result.change_info) {
        const newChange = {
          id: Date.now(),
          device_id: device.id,
          change_time: result.change_info.change_time,
          change_type: result.change_info.change_type,
          description: result.change_info.description,
          details: result.change_info.details
        }
        
        configChanges.value.unshift(newChange)
        
        // 显示通知
        ElMessage.warning({
          message: `设备 ${device.name} 检测到配置变更: ${newChange.description}`,
          duration: 5000
        })
      }
      
      // 更新最后检查时间
      device.last_check_time = result.check_time
      
    } else {
      console.warn(`设备 ${device.name} 配置检测失败:`, result.message)
      if (result.message && !result.message.includes('设备离线')) {
        ElMessage.warning(`设备 ${device.name}: ${result.message}`)
      }
    }
    
  } catch (error) {
    console.error(`检查设备配置失败 (${device.name}):`, error)
    ElMessage.error(`检查设备 ${device.name} 配置失败: ${error.message}`)
  }
}

// 批量开始监控
const startBatchMonitoring = async () => {
  batchDialogVisible.value = true
  batchProgress.value = 0
  batchStatus.value = ''
  batchProgressText.value = '准备开始批量监控...'
  batchResults.value = []

  const onlineDevicesList = onlineDevices.value.filter(device => device.monitoring_status !== 'monitoring')
  
  if (onlineDevicesList.length === 0) {
    ElMessage.info('没有可以开始监控的设备')
    batchDialogVisible.value = false
    return
  }

  const total = onlineDevicesList.length
  let completed = 0

  for (const device of onlineDevicesList) {
    try {
      // 设置默认监控参数
      device.monitoring_status = 'monitoring'
      device.monitoring_interval = 10
      device.last_check_time = new Date().toISOString()
      
      // 启动监控
      startDeviceMonitoring(device)
      
      batchResults.value.push({
        name: device.name,
        ip_address: device.ip_address,
        status: 'success',
        message: '监控已启动'
      })
      
    } catch (error) {
      batchResults.value.push({
        name: device.name,
        ip_address: device.ip_address,
        status: 'failed',
        message: error.message
      })
    }
    
    completed++
    batchProgress.value = Math.round((completed / total) * 100)
    batchProgressText.value = `正在设置监控... (${completed}/${total})`
    
    // 添加小延迟避免过快
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  batchStatus.value = 'success'
  batchProgressText.value = `批量监控设置完成！成功: ${batchResults.value.filter(r => r.status === 'success').length}, 失败: ${batchResults.value.filter(r => r.status === 'failed').length}`
  
  // 保存监控状态到localStorage
  saveMonitoringState()
  
  ElMessage.success('批量监控设置完成')
}

// 停止所有监控
const stopAllMonitoring = async () => {
  try {
    const result = await ElMessageBox.confirm(
      '确定要停止所有设备的监控吗？',
      '确认操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    if (result === 'confirm') {
      // 停止所有定时器
      monitoringTimers.value.forEach((timer, deviceId) => {
        clearInterval(timer)
      })
      monitoringTimers.value.clear()

      // 更新所有设备状态
      devices.value.forEach(device => {
        if (device.monitoring_status === 'monitoring') {
          device.monitoring_status = 'stopped'
          device.last_check_time = new Date().toISOString()
        }
      })

      ElMessage.success('已停止所有设备监控')
    }
  } catch (error) {
    // 用户取消操作
  }
}

// 查看配置变更
const viewConfigChanges = (device) => {
  selectedDevice.value = device
  deviceChanges.value = configChanges.value.filter(change => change.device_id === device.id)
  changesDialogVisible.value = true
}

// 刷新变更记录
const refreshChanges = async () => {
  if (selectedDevice.value) {
    await loadConfigChanges()
    deviceChanges.value = configChanges.value.filter(change => change.device_id === selectedDevice.value.id)
  }
}

// 查看变更详情
const viewChangeDetail = (change) => {
  ElMessageBox.alert(change.details || '暂无详细信息', '变更详情', {
    confirmButtonText: '确定'
  })
}

// 导出数据
const exportData = () => {
  try {
    const exportData = {
      devices: devices.value,
      configChanges: configChanges.value,
      exportTime: new Date().toISOString()
    }
    
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `config-monitoring-${new Date().toISOString().split('T')[0]}.json`
    link.click()
    
    ElMessage.success('数据导出成功')
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败: ' + error.message)
  }
}

// 筛选处理
const handleFilter = () => {
  currentPage.value = 1
}

const resetFilter = () => {
  filterForm.value = {
    name: '',
    ip: '',
    type: '',
    monitoring_status: '',
    online_status: ''
  }
  currentPage.value = 1
}

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
}

const handleCurrentChange = (val) => {
  currentPage.value = val
}

// 选择处理
const handleSelectionChange = (val) => {
  selectedDevices.value = val
}

// 页面可见性检测
const handleVisibilityChange = () => {
  if (!document.hidden) {
    // 页面变为可见时，检查并恢复监控状态
    restoreMonitoringState()
  }
}

// 生命周期
onMounted(async () => {
  await refreshDevices()
  // 恢复监控状态
  restoreMonitoringState()
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
})

onUnmounted(() => {
  // 保存监控状态
  saveMonitoringState()
  
  // 移除页面可见性监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  
  // 清理所有定时器
  monitoringTimers.value.forEach((timer) => {
    clearInterval(timer)
  })
  monitoringTimers.value.clear()
})
</script>

<style scoped>
.config-monitoring-container {
  padding: 20px;
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.filter-form {
  margin: 0;
}

/* 现代化帮助说明样式 */
.help-section-modern {
  margin-bottom: 20px;
}

.help-content-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.help-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.help-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.help-header h4 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.help-items {
  display: grid;
  gap: 12px;
}

.help-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.help-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.help-item.highlight {
  background: rgba(255, 193, 7, 0.2);
  border-color: rgba(255, 193, 7, 0.4);
}

.item-icon {
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.help-item span:last-child {
  line-height: 1.5;
  font-size: 14px;
}

.stats-section {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 6px;
  border: 1px solid #e1f5fe;
}

.monitoring-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.monitoring {
  background-color: #67c23a;
  animation: pulse 2s infinite;
}

.status-indicator.not-monitoring {
  background-color: #909399;
}

.status-indicator.failed {
  background-color: #f56c6c;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 2px;
  padding: 1px 0;
  align-items: center;
  justify-content: center;
}

.action-buttons .el-button {
  margin: 0;
  padding: 3px 6px;
  font-size: 11px;
  min-width: 60px;
}

.monitor-btn {
  display: flex;
  align-items: center;
  gap: 3px;
}

.status-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1px 0;
}

.monitoring-status-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.config-changes-cell {
  display: flex;
  justify-content: center;
}

.no-changes {
  color: #909399;
  font-size: 12px;
}

.offline-tip {
  margin-top: 2px;
  text-align: center;
  font-size: 10px;
  color: #909399;
  line-height: 1.2;
}

.action-buttons .el-button:disabled {
  opacity: 0.5;
}

/* 表格样式优化 */
.el-table {
  font-size: 14px;
}

.el-table .el-table__cell {
  text-align: center;
  vertical-align: middle;
  padding: 6px 0;
}

.el-table th.el-table__cell {
  text-align: center;
  vertical-align: middle;
  font-weight: 600;
  background-color: #fafafa;
  height: 38px;
}

.el-table td.el-table__cell {
  text-align: center;
  vertical-align: middle;
  height: 42px;
}

/* 离线设备行样式 */
.el-table .el-table__row.offline-row {
  background-color: #fafafa;
  color: #909399;
}

.el-table .el-table__row.offline-row:hover > td {
  background-color: #f5f5f5 !important;
}

.pagination-section {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.monitoring-settings {
  padding: 10px 0;
}

.changes-content {
  max-height: 500px;
  overflow-y: auto;
}

.changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.changes-header h4 {
  margin: 0;
  color: #303133;
}

.batch-progress {
  margin-bottom: 20px;
}

.progress-text {
  text-align: center;
  margin-top: 10px;
  color: #606266;
}

.batch-results {
  margin-top: 20px;
}

.dialog-footer {
  text-align: right;
}

.config-changes-list {
  max-height: 400px;
  overflow-y: auto;
}

.change-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  margin-bottom: 8px;
  background-color: #fafafa;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.change-time {
  font-size: 12px;
  color: #909399;
}

.change-content {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background-color: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}
</style>