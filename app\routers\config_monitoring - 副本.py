from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import func
import logging
import datetime
import json
import hashlib

from app import schemas, crud
from app.database import get_db
from app.models import Device as DeviceModel
from app.utils.device_connector import DeviceConnector
from pydantic import BaseModel

# 使用APIRouter创建路由器
router = APIRouter()
logger = logging.getLogger(__name__)

# 配置监控相关的数据模型
class ConfigMonitoringRequest(BaseModel):
    device_id: int
    interval: int  # 监控间隔（分钟）
    enable_notification: bool = True

class ConfigChangeDetection(BaseModel):
    device_id: int
    device_info: Dict[str, Any]  # 设备连接信息

class ConfigChange(BaseModel):
    id: int
    device_id: int
    change_time: str
    change_type: str  # added, modified, deleted
    description: str
    details: str
    config_hash: Optional[str] = None

@router.post("/check_config_changes")
async def check_config_changes(
    request: ConfigChangeDetection,
    db: Session = Depends(get_db)
):
    """
    检测设备配置变更
    """
    try:
        # 获取设备信息
        device = db.query(DeviceModel).filter(DeviceModel.id == request.device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        # 检查设备是否在线
        if not device.is_active:
            return {
                "status": "error",
                "message": "设备离线，无法检测配置变更",
                "has_changes": False
            }
        
        # 获取当前配置
        current_config = await get_device_current_config(device, request.device_info)
        
        if current_config["status"] != "success":
            return {
                "status": "error",
                "message": f"获取设备配置失败: {current_config.get('message', '未知错误')}",
                "has_changes": False
            }
        
        # 计算配置哈希值
        config_content = current_config["config"]
        current_hash = hashlib.md5(config_content.encode('utf-8')).hexdigest()
        
        # 获取上次保存的配置哈希值
        last_hash = device.config_hash if hasattr(device, 'config_hash') else None
        
        # 检测是否有变更
        has_changes = last_hash is not None and last_hash != current_hash
        
        result = {
            "status": "success",
            "has_changes": has_changes,
            "current_hash": current_hash,
            "last_hash": last_hash,
            "check_time": datetime.datetime.now().isoformat()
        }
        
        # 如果检测到变更，记录变更信息
        if has_changes:
            change_info = {
                "device_id": device.id,
                "change_time": datetime.datetime.now().isoformat(),
                "change_type": "modified",
                "description": "检测到配置变更",
                "details": f"配置哈希值从 {last_hash} 变更为 {current_hash}",
                "config_hash": current_hash
            }
            result["change_info"] = change_info
        
        # 更新设备的配置哈希值
        device.config_hash = current_hash
        device.config_last_checked = datetime.datetime.now()
        
        db.commit()
        
        return result
        
    except Exception as e:
        logger.error(f"检测配置变更失败: {str(e)}")
        return {
            "status": "error",
            "message": f"检测配置变更失败: {str(e)}",
            "has_changes": False
        }

async def get_device_current_config(device: DeviceModel, device_info: Dict[str, Any]):
    """
    获取设备当前配置
    """
    try:
        import netmiko
        from netmiko import ConnectHandler
        
        # 构建设备连接信息
        manufacturer = device.manufacturer.lower() if device.manufacturer else "cisco"
        
        # 根据设备厂商选择合适的命令和设备类型
        if "huawei" in manufacturer or "华为" in manufacturer:
            config_command = "display current-configuration"
            device_type = "huawei"
        elif "cisco" in manufacturer or "思科" in manufacturer:
            config_command = "show running-config"
            device_type = "cisco_ios"
        elif "h3c" in manufacturer or "华三" in manufacturer:
            config_command = "display current-configuration"
            device_type = "hp_comware"
        else:
            config_command = "show running-config"  # 默认使用Cisco命令
            device_type = "cisco_ios"
        
        # 构建连接参数
        connection_params = {
            'device_type': device_type,
            'host': device.ip_address,
            'username': device_info.get("username", device.username or "admin"),
            'password': device_info.get("password", device.password or "admin"),
            'port': device_info.get("port", device.port or 22),
            'timeout': device_info.get("timeout", 30),
            'session_timeout': 60,
            'auth_timeout': 30
        }
        
        # 连接设备并获取配置
        conn = ConnectHandler(**connection_params)
        config_output = conn.send_command(config_command, delay_factor=2)
        conn.disconnect()
        
        return {
            "status": "success",
            "config": config_output,
            "command": config_command
        }
            
    except Exception as e:
        logger.error(f"获取设备配置失败: {str(e)}")
        return {
            "status": "error",
            "message": f"获取设备配置失败: {str(e)}"
        }

@router.get("/config_changes/{device_id}")
def get_config_changes(
    device_id: int,
    skip: int = 0,
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """
    获取设备的配置变更历史
    """
    try:
        # 这里应该从数据库中获取配置变更记录
        # 由于当前数据库模型可能没有配置变更表，我们返回模拟数据
        # 在实际应用中，需要创建配置变更记录表
        
        changes = [
            {
                "id": 1,
                "device_id": device_id,
                "change_time": (datetime.datetime.now() - datetime.timedelta(hours=2)).isoformat(),
                "change_type": "modified",
                "description": "VLAN配置修改",
                "details": "VLAN 100 描述从 'Test VLAN' 修改为 'Production VLAN'",
                "config_hash": "abc123def456"
            },
            {
                "id": 2,
                "device_id": device_id,
                "change_time": (datetime.datetime.now() - datetime.timedelta(days=1)).isoformat(),
                "change_type": "added",
                "description": "新增ACL规则",
                "details": "新增ACL规则 permit tcp any host ************* eq 80",
                "config_hash": "def456ghi789"
            }
        ]
        
        return {
            "status": "success",
            "changes": changes[skip:skip+limit],
            "total": len(changes)
        }
        
    except Exception as e:
        logger.error(f"获取配置变更历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置变更历史失败: {str(e)}")

@router.post("/start_monitoring")
def start_monitoring(
    request: ConfigMonitoringRequest,
    db: Session = Depends(get_db)
):
    """
    开始监控设备配置
    """
    try:
        # 获取设备信息
        device = db.query(DeviceModel).filter(DeviceModel.id == request.device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        # 检查设备是否在线
        if not device.is_active:
            raise HTTPException(status_code=400, detail="设备离线，无法开始监控")
        
        # 这里应该启动监控任务
        # 在实际应用中，可以使用Celery或其他任务队列来处理定时监控
        
        return {
            "status": "success",
            "message": f"已开始监控设备 {device.name}，监控间隔: {request.interval} 分钟",
            "device_id": device.id,
            "interval": request.interval,
            "start_time": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"开始监控失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"开始监控失败: {str(e)}")

@router.post("/stop_monitoring/{device_id}")
def stop_monitoring(
    device_id: int,
    db: Session = Depends(get_db)
):
    """
    停止监控设备配置
    """
    try:
        # 获取设备信息
        device = db.query(DeviceModel).filter(DeviceModel.id == device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        # 这里应该停止监控任务
        # 在实际应用中，需要停止相应的定时任务
        
        return {
            "status": "success",
            "message": f"已停止监控设备 {device.name}",
            "device_id": device.id,
            "stop_time": datetime.datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止监控失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止监控失败: {str(e)}")

@router.get("/monitoring_status/{device_id}")
def get_monitoring_status(
    device_id: int,
    db: Session = Depends(get_db)
):
    """
    获取设备监控状态
    """
    try:
        # 获取设备信息
        device = db.query(DeviceModel).filter(DeviceModel.id == device_id).first()
        if not device:
            raise HTTPException(status_code=404, detail="设备不存在")
        
        # 这里应该从监控系统中获取实际的监控状态
        # 目前返回模拟状态
        
        return {
            "status": "success",
            "device_id": device.id,
            "device_name": device.name,
            "monitoring_status": "stopped",  # monitoring, stopped, error
            "last_check_time": None,
            "monitoring_interval": None,
            "error_message": None
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取监控状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取监控状态失败: {str(e)}")