<template>
  <div class="page-container full-width">
    <div class="page-header">
      <div class="page-actions">
        <!-- 添加机房管理和机柜管理按钮 -->
        <el-button class="btn-primary" style="background-color: #409EFF;" @click="navigateToDatacenterManagement">
          <el-icon><House /></el-icon> 机房管理
        </el-button>
        <el-button class="btn-primary" style="background-color: #409EFF;" @click="navigateToRackManagement">
          <el-icon><Grid /></el-icon> 机柜管理
        </el-button>
        <!-- 原有的添加设备按钮 -->
        <el-button class="btn-primary" style="background-color: #409EFF;" @click="openAddDeviceDialog">
          <el-icon><Plus /></el-icon> 添加设备
        </el-button>
        <el-button class="btn-warning" style="background-color: #F56C6C;" @click="refreshDevicesStatus">
          <el-icon><Refresh /></el-icon> 全量刷新
        </el-button>
        <el-button class="btn-primary" style="background-color: #409EFF;" @click="updateDeviceSystemInfo">
          <el-icon><Monitor /></el-icon> 更新信息
        </el-button>
        <el-button class="btn-success" style="background-color: #67C23A;" @click="openImportDialog">
          <el-icon><Upload /></el-icon> 导入
        </el-button>
        <el-button class="btn-info" style="background-color: #909399;" @click="exportAllDevices" :loading="exportLoading">
          <el-icon><Download /></el-icon> 设备
        </el-button>
        <!-- <el-button class="btn-warning" style="background-color: #E6A23C;" @click="detectIpConflicts">
          <el-icon><Warning /></el-icon> IP冲突检测
        </el-button> -->

        <el-dropdown @command="handleRefreshCommand" trigger="click">
          <el-button class="btn-primary" style="background-color: #409EFF;" :icon="Timer">
            {{ autoRefreshEnabled 
              ? `自动刷新中(${countdownSeconds}/${autoRefreshInterval}秒)` 
              : '自动刷新' }}
            <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="toggle">
                {{ autoRefreshEnabled ? '关闭自动刷新状态' : '开启自动刷新状态' }}
              </el-dropdown-item>
              <el-dropdown-item divided disabled v-if="!autoRefreshEnabled">选择刷新间隔</el-dropdown-item>
              <el-dropdown-item 
                :class="{ 'is-active': autoRefreshEnabled && autoRefreshInterval === 10 }" 
                command="10"
                :disabled="!autoRefreshEnabled">
                10秒
              </el-dropdown-item>
              <el-dropdown-item 
                :class="{ 'is-active': autoRefreshEnabled && autoRefreshInterval === 30 }" 
                command="30"
                :disabled="!autoRefreshEnabled">
                30秒
              </el-dropdown-item>
              <el-dropdown-item 
                :class="{ 'is-active': autoRefreshEnabled && autoRefreshInterval === 60 }" 
                command="60"
                :disabled="!autoRefreshEnabled">
                1分钟
              </el-dropdown-item>
              <el-dropdown-item 
                :class="{ 'is-active': autoRefreshEnabled && autoRefreshInterval === 300 }" 
                command="300"
                :disabled="!autoRefreshEnabled">
                5分钟
              </el-dropdown-item>
              <el-dropdown-item 
                command="custom" 
                divided
                :disabled="!autoRefreshEnabled">
                自定义间隔...
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <el-card class="combined-card">
      <!-- 现代化搜索筛选区域 -->
      <div class="filter-container">
        <!-- 筛选表单项 -->
        <div class="filter-fields">
          <el-form :inline="true" :model="filterForm" class="device-filter-form">
            <el-form-item class="compact-form-item">
              <el-input 
                v-model="filterForm.name" 
                placeholder="设备名称" 
                clearable
                prefix-icon="Search"
                @input="handleNameInput"
                @change="debouncedSearch"
                class="input-medium"
              />
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-input 
                v-model="filterForm.ip" 
                placeholder="IP地址" 
                clearable
                prefix-icon="Location"
                @input="handleIpInput"
                @change="debouncedSearch"
                class="input-small"
              />
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-select
                v-model="filterForm.type"
                placeholder="设备类型"
                clearable
                class="select-large"
              >
                <el-option
                  v-for="(label, value) in deviceTypeOptions"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-input 
                v-model="filterForm.serial_number" 
                placeholder="序列号" 
                clearable
                prefix-icon="Document"
                @input="handleSerialInput"
                @change="debouncedSearch"
                class="input-medium"
              />
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-select
                v-model="filterForm.manufacturer"
                placeholder="厂商"
                clearable
                class="select-medium"
              >
                <el-option
                  v-for="(label, value) in manufacturerOptions"
                  :key="value"
                  :label="label"
                  :value="value"
                />
              </el-select>
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-select 
                v-model="filterForm.status" 
                placeholder="状态" 
                clearable
                class="select-small"
                @change="debouncedSearch"
              >
                <el-option label="在线" value="active" />
                <el-option label="离线" value="inactive" />
              </el-select>
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-select 
                v-model="filterForm.datacenter" 
                placeholder="机房" 
                clearable
                filterable
                @change="handleDatacenterFilterChange"
                class="select-medium"
              >
                <el-option 
                  v-for="dc in datacenters" 
                  :key="dc.id" 
                  :label="dc.name" 
                  :value="dc.name" 
                />
              </el-select>
            </el-form-item>
            <el-form-item class="compact-form-item">
              <el-select 
                v-model="filterForm.rack" 
                placeholder="机柜" 
                clearable
                filterable
                :disabled="!filterForm.datacenter"
                @change="debouncedSearch"
                class="select-small"
              >
                <el-option 
                  v-for="rack in filteredFilterRacks" 
                  :key="rack.id" 
                  :label="rack.id" 
                  :value="rack.id" 
                />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 操作按钮组 -->
        <div class="filter-buttons">
          <el-button @click="resetFilter" size="default" class="filter-btn">
            <el-icon><Refresh /></el-icon> 重置
          </el-button>
          <el-button type="primary" @click="saveCurrentFilter" size="default" class="filter-btn">
            <el-icon><Star /></el-icon> 保存筛选
          </el-button>
          <el-button type="info" @click="openColumnSelector" size="default" class="filter-btn">
            <el-icon><SetUp /></el-icon> 自选列
          </el-button>
        </div>
      </div>
      
      <div v-if="savedFilters.length > 0" class="saved-filters">
        <span>已保存的筛选: </span>
        <el-tag
          v-for="filter in savedFilters"
          :key="filter.id"
          class="saved-filter-tag"
          effect="plain"
          @click="applyFilter(filter)"
          closable
          @close="deleteFilter(filter, $event)"
        >
          {{ filter.name }}
        </el-tag>
        
        <el-button size="small" type="success" plain @click="exportFilteredDevices" :disabled="devices.length === 0">
          <el-icon><Download /></el-icon> 导出当前结果
        </el-button>
      </div>
      
      <!-- <div class="table-view-selector">
        <el-button-group>
          <el-button type="primary" :icon="View" plain>详细视图</el-button>
          <el-button type="primary" :icon="Menu" plain>列表视图</el-button>
        </el-button-group>
      </div> -->
      
      <div class="sorting-info">
        <el-alert
          type="info"
          :closable="false"
          show-icon
          class="custom-alert"
        >
          <template #title>
            <div class="sorting-info-container">
              <span v-html="sortingInfoText"></span>
              <el-button 
                v-if="sortColumn && sortOrder" 
                size="small" 
                class="reset-sort-btn" 
                @click="resetSorting"
              >
                恢复默认排序
              </el-button>
            </div>
          </template>
        </el-alert>
      </div>
      
      <el-table
        v-loading="loading"
        :data="devices"
        stripe
        style="width: 100%;"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        header-cell-class-name="table-header-cell"
        border
        :fit="true"
        table-layout="fixed"
        row-class-name="device-table-row"
        highlight-current-row
        :default-sort="defaultSort"
        :cell-style="{
          textAlign: 'center',
          verticalAlign: 'middle',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          padding: '8px 12px'
        }"
        :header-cell-style="{
          'background-color': '#f5f7fa',
          'color': '#303133',
          'font-weight': '600',
          'white-space': 'nowrap',
          'padding': '8px 12px',
          'height': '46px',
          'font-size': '13px',
          'cursor': 'pointer',
          'position': 'relative',
          'text-align': 'center',
          'vertical-align': 'middle',
          'border-bottom': '1px solid #ebeef5'
        }"
      >
        <el-table-column type="selection" width="70" align="center"/>
        
        <!-- 使用columnOrder动态渲染列 -->
        <template v-for="key in columnOrder" :key="key">
          <!-- 序号列 -->
          <el-table-column 
            v-if="key === 'index' && visibleColumns.index" 
            type="index" 
            width="60" 
            align="center" 
            label="序号" 
            header-align="center"
            label-class-name="single-line-header"
            sortable="custom"
          />
          
          <!-- 厂商列 -->
          <el-table-column
            v-if="key === 'manufacturer' && visibleColumns.manufacturer"
            prop="manufacturer"
            align="center"
            label="厂商"
            min-width="80"
            width="90"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ manufacturerOptions[scope.row.manufacturer] || scope.row.manufacturer }}
            </template>
          </el-table-column>

          <!-- 设备类型列 -->
          <el-table-column
            v-if="key === 'device_type' && visibleColumns.device_type"
            prop="device_type"
            align="center"
            label="类型"
            min-width="80"
            width="85"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ getDeviceTypeLabel(scope.row.device_type) }}
            </template>
          </el-table-column>

          <!-- 设备名称列 -->
          <el-table-column
            v-if="key === 'name' && visibleColumns.name"
            prop="name"
            align="center"
            label="设备名称"
            min-width="120"
            show-overflow-tooltip
            sortable="custom"
          />

          <!-- SN列 -->
          <el-table-column
            v-if="key === 'serial_number' && visibleColumns.serial_number"
            prop="serial_number"
            align="center"
            label="序列号"
            min-width="120"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="scope">
              {{ scope.row.serial_number || '-' }}
            </template>
          </el-table-column>

          <!-- 型号列 -->
          <el-table-column
            v-if="key === 'model' && visibleColumns.model"
            prop="model"
            align="center"
            label="型号"
            min-width="100"
            show-overflow-tooltip
            sortable="custom"
          />

          <!-- IP地址列 -->
          <el-table-column
            v-if="key === 'ip_address' && visibleColumns.ip_address"
            prop="ip_address"
            align="center"
            label="IP地址"
            min-width="120"
            sortable="custom"
            show-overflow-tooltip
          />

          <!-- 新增：IP使用情况列 -->
          <el-table-column
            v-if="key === 'ip_usage' && visibleColumns.ip_usage"
            label="IP使用情况"
            min-width="180"
            align="center"
            show-overflow-tooltip
          >
            <template #default="scope">
              <div v-if="scope.row.ip_info" class="ip-usage-display">
                <div class="ip-status-tags">
                  <el-tag
                    size="small"
                    :type="getIPStatusType(scope.row.ip_info.status)"
                  >
                    {{ getIPStatusText(scope.row.ip_info.status) }}
                  </el-tag>
                  <el-tag
                    v-if="scope.row.ip_info.is_primary"
                    size="small"
                    type="success"
                    style="margin-left: 4px;"
                  >
                    主IP
                  </el-tag>
                </div>
                <div class="ip-subnet-info">
                  {{ scope.row.ip_info.subnet_name || '-' }}
                </div>
              </div>
              <div v-else class="ip-no-info">
                <el-button
                  size="small"
                  type="primary"
                  plain
                  @click="associateIPAddress(scope.row)"
                  v-if="scope.row.ip_address"
                >
                  关联IP
                </el-button>
                <span v-else class="text-gray-400">无IP</span>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column
            v-if="key === 'status' && visibleColumns.status"
            prop="is_active"
            align="center"
            label="状态"
            min-width="100"
            width="100"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="status-indicator" :class="scope.row.is_active ? 'status-online' : 'status-offline'">
                <span class="status-dot"></span>
                {{ scope.row.is_active ? '在线' : '离线' }}
              </span>
            </template>
          </el-table-column>

          <!-- 连接方式列 -->
          <el-table-column
            v-if="key === 'protocol' && visibleColumns.protocol"
            prop="protocol"
            align="center"
            label="连接方式"
            min-width="90"
            width="95"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              <span class="connection-type">
                {{ scope.row.protocol === 'ssh' ? 'SSH' : 'Telnet' }}
              </span>
            </template>
          </el-table-column>

          <!-- 端口列 -->
          <el-table-column
            v-if="key === 'port' && visibleColumns.port"
            prop="port"
            align="center"
            label="端口"
            min-width="70"
            width="70"
            sortable="custom"
            show-overflow-tooltip
          />

          <!-- 机房列 -->
          <el-table-column
            v-if="key === 'datacenter' && visibleColumns.datacenter"
            prop="datacenter"
            align="center"
            label="机房"
            min-width="100"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="scope">
              {{ scope.row.datacenter || '-' }}
            </template>
          </el-table-column>

          <!-- 机柜列 -->
          <el-table-column
            v-if="key === 'rack' && visibleColumns.rack"
            prop="rack"
            align="center"
            label="机柜"
            min-width="80"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="scope">
              {{ scope.row.rack || '-' }}
            </template>
          </el-table-column>

          <!-- U位列 -->
          <el-table-column
            v-if="key === 'position' && visibleColumns.position"
            prop="position"
            align="center"
            label="U位"
            min-width="50"
            width="70"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.position || '-' }}
            </template>
          </el-table-column>

          <!-- CPU使用率列 -->
          <el-table-column
            v-if="key === 'cpu_usage' && visibleColumns.cpu_usage"
            prop="cpu_usage"
            align="center"
            label="CPU"
            min-width="80"
            width="80"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              <template v-if="scope.row.cpu_usage">
                <span class="usage-value-only" :style="{ color: getCpuTextColor(scope.row.cpu_usage) }">
                  {{ parseInt(scope.row.cpu_usage) || 0 }}%
                </span>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 内存使用率列 -->
          <el-table-column
            v-if="key === 'memory_usage' && visibleColumns.memory_usage"
            prop="memory_usage"
            align="center"
            label="内存"
            min-width="80"
            width="80"
            sortable="custom"
            show-overflow-tooltip
          >
            <template #default="scope">
              <template v-if="scope.row.memory_usage">
                <span class="usage-value-only" :style="{ color: getMemoryTextColor(scope.row.memory_usage) }">
                  {{ parseInt(scope.row.memory_usage) || 0 }}%
                </span>
              </template>
              <span v-else>-</span>
            </template>
          </el-table-column>

          <!-- 版本信息列 -->
          <el-table-column
            v-if="key === 'version_info' && visibleColumns.version_info"
            prop="version_info"
            align="center"
            label="版本信息"
            min-width="120"
            show-overflow-tooltip
            sortable="custom"
          >
            <template #default="scope">
              {{ scope.row.version_info || '-' }}
            </template>
          </el-table-column>
        </template>
        
        <!-- 操作列总是显示 -->
        <el-table-column align="center" label="操作" width="220" fixed="right">
          <template #default="scope">
            <div class="operation-buttons-inline">
              <el-tooltip content="详情" placement="top" :enterable="false">
                <el-button
                  circle
                  size="small"
                  type="primary"
                  @click="handleDeviceDetail(scope.row)"
                >
                  <el-icon><View /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="编辑" placement="top" :enterable="false">
                <el-button
                  circle
                  size="small"
                  type="warning"
                  @click="handleDeviceEdit(scope.row)"
                >
                  <el-icon><EditPen /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip :content="scope.row.is_active ? '登录' : '设备离线'" placement="top" :enterable="false">
                <el-button
                  circle
                  size="small"
                  type="success"
                  @click="handleDeviceLogin(scope.row)"
                  :disabled="!scope.row.is_active"
                >
                  <el-icon><Monitor /></el-icon>
                </el-button>
              </el-tooltip>

              <el-tooltip content="删除" placement="top" :enterable="false">
                <el-button
                  circle
                  size="small"
                  type="danger"
                  @click="confirmDelete(scope.row)"
                >
                  <el-icon><Delete /></el-icon>
                </el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="table-footer">
        <el-button-group>
          <el-button type="danger" :disabled="!multipleSelection.length" @click="handleBatchDelete" class="batch-action-btn">
            <el-icon><Delete /></el-icon> 批量删除
          </el-button>
          <!-- <el-button type="primary" plain :disabled="!multipleSelection.length">批量配置</el-button> -->
          <el-button type="primary" :disabled="!multipleSelection.length" @click="exportSelectedDevices" class="batch-action-btn">
            <el-icon><Download /></el-icon> 导出所选设备
          </el-button>
        </el-button-group>
        
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :pager-count="5"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          @prev-click="handlePrevClick"
          @next-click="handleNextClick"
        />
      </div>
    </el-card>
    
    <el-dialog
      v-model="deviceDialogVisible"
      :title="dialogType === 'add' ? '添加设备' : '编辑设备'"
      width="800px"
    >
      <el-form
        ref="deviceFormRef"
        :model="deviceForm"
        :rules="deviceRules"
        label-width="100px"
        class="two-column-form"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备名称" prop="name">
              <el-input v-model="deviceForm.name" placeholder="请输入设备名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ip_address">
              <el-input v-model="deviceForm.ip_address" placeholder="请输入IP地址" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备类型" prop="device_type">
              <div style="display: flex; gap: 15px;">
                <el-select v-model="deviceForm.device_type" placeholder="请选择设备类型" style="width: 150px;">
                  <el-option
                    v-for="(label, value) in deviceTypeOptions"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
                <el-button type="primary" @click="openCustomTypeDialog" style="flex-shrink: 0; background-color: #409EFF; border-color: #409EFF; color: white;">
                  <el-icon><Plus /></el-icon> 自定义
                </el-button>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="厂商" prop="manufacturer">
              <div style="display: flex; gap: 15px;">
                <el-select v-model="deviceForm.manufacturer" placeholder="请选择厂商" style="width: 150px;">
                  <el-option
                    v-for="(label, value) in manufacturerOptions"
                    :key="value"
                    :label="label"
                    :value="value"
                  />
                </el-select>
                <el-button type="primary" @click="openCustomManufacturerDialog" style="flex-shrink: 0; background-color: #409EFF; border-color: #409EFF; color: white;">
                  <el-icon><Plus /></el-icon> 自定义
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="型号" prop="model">
              <el-input v-model="deviceForm.model" placeholder="请输入设备型号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="连接方式" prop="connection_type">
              <el-select v-model="deviceForm.connection_type" placeholder="请选择连接方式" style="width: 100%">
                <el-option label="SSH" value="ssh" />
                <el-option label="Telnet" value="telnet" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="登录用户名" prop="username">
              <el-input v-model="deviceForm.username" placeholder="请输入登录用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="登录密码" prop="password">
              <el-input v-model="deviceForm.password" type="password" placeholder="请输入登录密码" show-password />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="序列号" prop="serial_number">
              <el-input v-model="deviceForm.serial_number" placeholder="请输入设备序列号(SN)" maxlength="50" show-word-limit />
              <div class="form-tip">
                <small class="text-muted">
                  序列号必须是唯一的，系统会自动验证
                </small>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="port">
              <el-input-number v-model="deviceForm.port" :min="1" :max="65535" placeholder="请输入端口号" style="width: 100%" />
              <div class="form-tip">
                <small class="text-muted">
                  SSH默认端口22，Telnet默认端口23，可手动修改
                </small>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="机房" prop="datacenter">
              <el-select 
                v-model="deviceForm.datacenter" 
                placeholder="请选择机房" 
                style="width: 100%"
                :loading="loadingDatacenters"
                filterable
              >
                <el-option
                  v-for="dc in datacenters"
                  :key="dc.id"
                  :label="dc.name"
                  :value="dc.name"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="机柜" prop="rack">
              <el-select 
                v-model="deviceForm.rack" 
                placeholder="请选择机柜" 
                style="width: 100%"
                :loading="loadingRacks"
                filterable
                :disabled="!deviceForm.datacenter"
                @change="handleRackChange"
              >
                <el-option
                  v-for="rack in filteredRacks"
                  :key="rack.id"
                  :label="rack.id"
                  :value="rack.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="U位" prop="position">
              <div class="position-input-container">
                <el-input-number 
                  v-model="deviceForm.position" 
                  :min="0" 
                  :max="42" 
                  placeholder="请输入U位" 
                  style="width: 100%" 
                  @change="checkPositionAvailability"
                  :class="{'position-input-danger': isCurrentPositionOccupied}"
                />
                <el-tooltip 
                  v-if="deviceForm.rack && occupiedPositions.length > 0"
                  effect="light" 
                  placement="top"
                  :content="`机柜内已占用的位置: ${occupiedPositions.join(', ')}`"
                >
                  <el-icon class="position-info-icon">
                    <info-filled />
                  </el-icon>
                </el-tooltip>
              </div>
              <div v-if="isCurrentPositionOccupied" class="position-warning">
                <small>此位置已被占用，请选择其他位置</small>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 预留给其他字段 -->
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deviceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDeviceForm">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="customIntervalDialogVisible"
      title="设置自定义刷新间隔"
      width="400px"
    >
      <el-form :model="{ customInterval }" label-width="140px">
        <el-form-item label="刷新间隔(秒)">
          <el-input-number 
            v-model="customInterval" 
            :min="10" 
            :max="3600" 
            :step="5" 
          />
          <div class="form-tip mt-2">
            <small class="text-muted">
              建议设置较长的刷新间隔（≥30秒）以避免服务器压力过大
            </small>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="customIntervalDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="applyCustomInterval">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="importDialogVisible"
      title="批量导入设备"
      width="500px"
    >
      <el-upload
        ref="uploadRef"
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove"
        :before-upload="beforeUpload"
        :file-list="fileList"
        accept=".xlsx,.xls,.csv"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖拽文件到此处或 <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            支持Excel(.xlsx, .xls)和CSV(.csv)文件格式<br>
            <a href="#" @click.prevent="downloadTemplate">下载导入模板</a>
          </div>
        </template>
      </el-upload>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="importDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleBatchImport" :loading="importLoading">
            开始导入
          </el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      v-model="previewDialogVisible"
      title="预览导入设备"
      width="80%"
    >
      <p class="mb-3">以下是从CSV文件中解析出的设备信息。您可以点击"添加"按钮手动添加单个设备。</p>
      
      <el-table :data="previewDevices" stripe style="width: 100%">
        <el-table-column prop="name" label="设备名称" />
        <el-table-column prop="ip_address" label="IP地址" />
        <el-table-column prop="device_type" label="设备类型">
          <template #default="scope">
            {{ getDeviceTypeLabel(scope.row.device_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="manufacturer" label="厂商">
          <template #default="scope">
            {{ manufacturerOptions[scope.row.manufacturer] || scope.row.manufacturer }}
          </template>
        </el-table-column>
        <el-table-column prop="model" label="型号" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button 
              size="small" 
              type="primary" 
              @click="importSingleDevice(scope.row)"
            >
              添加
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="previewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="importDevicesFromPreview">
            批量导入全部
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自选显示列对话框 -->
    <el-dialog
      v-model="columnDialogVisible"
      title="自选显示列"
      width="700px"
    >
      <div class="column-selection">
        <p class="column-selection-tip">请选择要显示的列，拖动可调整顺序：</p>
        
        <!-- 修改为每行3个选项的布局 -->
        <el-row :gutter="20" class="column-options-row" ref="columnSortable">
          <el-col 
            v-for="(key, index) in columnOrder" 
            :key="key" 
            :span="8"
            class="column-option-col"
          >
            <div class="column-option-item" :data-id="key">
              <div class="column-option-content">
                <el-checkbox 
                  v-model="tempVisibleColumns[key]"
                  :disabled="key === 'name' && isLastSelectedColumn(key)"
                  class="column-checkbox"
                >
                  {{ columnOptions.find(opt => opt.key === key)?.label }}
                </el-checkbox>
                <div class="drag-handle">
                  <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="20" height="20">
                    <path fill="currentColor" d="M3,15V13H5V15H3M3,11V9H5V11H3M7,15V13H9V15H7M7,11V9H9V11H7M11,15V13H13V15H11M11,11V9H13V11H11M15,15V13H17V15H15M15,11V9H17V11H15M19,15V13H21V15H19M19,11V9H21V11H19Z" />
                  </svg>
                  <span class="drag-text">拖动</span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="resetColumnSelection" bg class="reset-button">恢复默认</el-button>
          <el-button @click="cancelColumnSelection" bg class="cancel-button">取消</el-button>
          <el-button type="primary" @click="saveColumnSelection">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 在页面底部添加设备详情对话框 -->
    <el-dialog
      v-model="deviceDetailVisible"
      title="设备详情"
      width="600px"
      destroy-on-close
    >
      <div v-if="currentDevice" class="device-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="设备名称" label-align="right" align="left">
            {{ currentDevice.name }}
          </el-descriptions-item>
          
          <el-descriptions-item label="IP地址" label-align="right" align="left">
            {{ currentDevice.ip_address }}
          </el-descriptions-item>
          
          <el-descriptions-item label="设备类型" label-align="right" align="left">
            <el-tag :type="getDeviceTypeTag(currentDevice.device_type)" effect="plain">
              {{ getDeviceTypeLabel(currentDevice.device_type) }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="厂商" label-align="right" align="left">
            {{ manufacturerOptions[currentDevice.manufacturer] || currentDevice.manufacturer }}
          </el-descriptions-item>
          
          <el-descriptions-item label="型号" label-align="right" align="left">
            {{ currentDevice.model || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="连接方式" label-align="right" align="left">
            <el-tag :type="currentDevice.protocol === 'ssh' ? 'success' : 'warning'" effect="light" size="small">
              {{ currentDevice.protocol === 'ssh' ? 'SSH' : 'Telnet' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="端口" label-align="right" align="left">
            {{ currentDevice.port }}
          </el-descriptions-item>
          
          <el-descriptions-item label="机房" label-align="right" align="left">
            {{ currentDevice.datacenter || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="机柜" label-align="right" align="left">
            {{ currentDevice.rack || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="U位" label-align="right" align="left">
            {{ currentDevice.position || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="序列号" label-align="right" align="left">
            {{ currentDevice.serial_number || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="状态" label-align="right" align="left">
            <el-tag :type="currentDevice.is_active ? 'success' : 'info'" effect="light" size="small">
              {{ currentDevice.is_active ? '在线' : '离线' }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="远程类型" label-align="right" align="left">
            {{ currentDevice.netmiko_device_type || currentDevice.remote_type || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="登录用户名" label-align="right" align="left">
            {{ currentDevice.username || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="登录密码" label-align="right" align="left">
            <span class="password-display">
              <span>{{ maskPassword(currentDevice.password) }}</span>
              <el-button 
                type="primary" 
                size="small" 
                link 
                v-if="currentDevice.password" 
                @click="togglePasswordVisibility"
              >
                {{ showPassword ? '隐藏' : '显示' }}
              </el-button>
            </span>
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间" label-align="right" align="left">
            {{ formatDate(currentDevice.created_at) || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="更新时间" label-align="right" align="left">
            {{ formatDate(currentDevice.updated_at) || '-' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="ID" label-align="right" align="left" :span="2">
            {{ currentDevice.id }}
          </el-descriptions-item>
          
          <el-descriptions-item v-if="currentDevice.description" label="备注" label-align="right" align="left" :span="2">
            {{ currentDevice.description }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="deviceDetailVisible = false">关闭</el-button>
          <el-button type="warning" @click="handleDeviceEdit(currentDevice)">编辑设备</el-button>
          <el-button 
            v-if="currentDevice && currentDevice.is_active" 
            type="primary" 
            @click="handleDeviceLogin(currentDevice)"
          >
            <el-icon><Link /></el-icon> 登录设备
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加终端对话框 -->
    <el-dialog
      v-model="terminalVisible"
      title="设备终端"
      width="80%"
      fullscreen
      :before-close="closeTerminal"
      destroy-on-close
    >
      <terminal-component 
        v-if="terminalVisible" 
        :device-info="terminalDevice" 
        @close="closeTerminal"
      />
    </el-dialog>

    <!-- 添加进度对话框 -->
    <el-dialog
      v-model="progressDialogVisible"
      title="更新设备系统信息"
      width="700px"
    >
      <el-progress :percentage="progressPercent" :status="progressSuccess ? 'success' : progressPercent < 100 ? '' : 'exception'" />
      <div class="progress-summary">
        <el-alert
          :title="progressSummary"
          :type="progressSuccess ? 'success' : 'info'"
          :closable="false"
          show-icon
        />
      </div>
      
      <el-divider>设备处理详情</el-divider>
      
      <el-table v-if="progressDetails.length > 0" :data="progressDetails" style="width: 100%" max-height="300px">
        <el-table-column prop="name" label="设备名称" width="120" />
        <el-table-column prop="ip" label="IP地址" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.status === 'success' ? 'success' : 
                    scope.row.status === 'processing' ? 'info' : 
                    scope.row.status === 'warning' ? 'warning' : 'danger'"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="message" label="详情" />
        <el-table-column label="采集结果" width="180">
          <template #default="scope">
            <div v-if="scope.row.cpu_usage">CPU: {{ scope.row.cpu_usage }}%</div>
            <div v-if="scope.row.memory_usage">内存: {{ scope.row.memory_usage }}%</div>
            <div v-if="scope.row.version">版本: {{ scope.row.version }}</div>
          </template>
        </el-table-column>
      </el-table>
      
      <div v-else class="no-details">
        <el-empty description="暂无处理详情" />
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="progressDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="progressDialogVisible = false; fetchDevices();" :disabled="!progressSuccess">
            {{ progressSuccess ? '完成' : '关闭' }}
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加机房对话框 -->
    <add-datacenter-dialog
      v-model:visible="addDatacenterDialogVisible"
      @add-datacenter="handleAddDatacenter"
    />

    <!-- 自定义设备类型对话框 -->
    <el-dialog
      v-model="customTypeDialogVisible"
      title="管理自定义设备类型"
      width="700px"
    >
      <el-tabs v-model="activeCustomTypeTab">
        <!-- 添加新类型标签页 -->
        <el-tab-pane label="添加新类型" name="add">
          <el-form :model="customTypeForm" label-width="100px">
            <el-form-item label="类型标识" required>
              <el-input
                v-model="customTypeForm.key"
                placeholder="请输入英文标识，如：ups、camera等"
                :maxlength="50"
              />
              <div class="form-tip">
                <small class="text-muted">
                  用于系统内部识别，建议使用英文小写字母和下划线
                </small>
              </div>
            </el-form-item>
            <el-form-item label="显示名称" required>
              <el-input
                v-model="customTypeForm.label"
                placeholder="请输入中文显示名称，如：UPS、摄像头等"
                :maxlength="20"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addCustomDeviceType">
                <el-icon><Plus /></el-icon> 添加设备类型
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 管理已有类型标签页 -->
        <el-tab-pane label="管理已有类型" name="manage">
          <div v-if="getCustomDeviceTypes().length === 0" class="empty-state">
            <el-empty description="暂无自定义设备类型" />
          </div>
          <div v-else>
            <el-table :data="getCustomDeviceTypes()" style="width: 100%">
              <el-table-column prop="key" label="类型标识" width="150" />
              <el-table-column prop="label" label="显示名称" width="150" />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="editCustomDeviceType(scope.row)">
                    <el-icon><EditPen /></el-icon> 编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteCustomDeviceType(scope.row.key)">
                    <el-icon><Delete /></el-icon> 删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="customTypeDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义厂商对话框 -->
    <el-dialog
      v-model="customManufacturerDialogVisible"
      title="管理自定义厂商"
      width="700px"
    >
      <el-tabs v-model="activeCustomManufacturerTab">
        <!-- 添加新厂商标签页 -->
        <el-tab-pane label="添加新厂商" name="add">
          <el-form :model="customManufacturerForm" label-width="100px">
            <el-form-item label="厂商标识" required>
              <el-input
                v-model="customManufacturerForm.key"
                placeholder="请输入英文标识，如：hp、dell等"
                :maxlength="50"
              />
              <div class="form-tip">
                <small class="text-muted">
                  用于系统内部识别，建议使用英文小写字母
                </small>
              </div>
            </el-form-item>
            <el-form-item label="厂商名称" required>
              <el-input
                v-model="customManufacturerForm.label"
                placeholder="请输入厂商名称，如：惠普、戴尔等"
                :maxlength="50"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="addCustomManufacturer">
                <el-icon><Plus /></el-icon> 添加厂商
              </el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <!-- 管理已有厂商标签页 -->
        <el-tab-pane label="管理已有厂商" name="manage">
          <div v-if="getCustomManufacturers().length === 0" class="empty-state">
            <el-empty description="暂无自定义厂商" />
          </div>
          <div v-else>
            <el-table :data="getCustomManufacturers()" style="width: 100%">
              <el-table-column prop="key" label="厂商标识" width="150" />
              <el-table-column prop="label" label="厂商名称" width="150" />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button size="small" type="primary" @click="editCustomManufacturer(scope.row)">
                    <el-icon><EditPen /></el-icon> 编辑
                  </el-button>
                  <el-button size="small" type="danger" @click="deleteCustomManufacturer(scope.row.key)">
                    <el-icon><Delete /></el-icon> 删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="customManufacturerDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- IP冲突检测对话框 -->
    <el-dialog
      v-model="ipConflictDialogVisible"
      title="IP冲突检测结果"
      width="80%"
      destroy-on-close
    >
      <div v-loading="ipConflictLoading" class="ip-conflict-content">
        <!-- 检测结果摘要 -->
        <el-alert
          :title="ipConflictData.message"
          :type="ipConflictData.total_conflicts > 0 ? 'warning' : 'success'"
          :closable="false"
          show-icon
          class="mb-4"
        >
          <template #default>
            <div class="conflict-summary">
              <p>总设备数: {{ ipConflictData.total_devices }}</p>
              <p>冲突IP数: {{ ipConflictData.total_conflicts }}</p>
              <p v-if="ipConflictData.total_conflicts > 0">
                影响设备数: {{ ipConflictData.conflicts.reduce((sum, conflict) => sum + conflict.device_count, 0) }}
              </p>
            </div>
          </template>
        </el-alert>

        <!-- 冲突详情表格 -->
        <div v-if="ipConflictData.total_conflicts > 0">
          <h3>冲突详情</h3>
          <div v-for="conflict in ipConflictData.conflicts" :key="conflict.ip_address" class="conflict-group mb-4">
            <el-card class="conflict-card">
              <template #header>
                <div class="conflict-header">
                  <span class="conflict-ip">IP地址: {{ conflict.ip_address }}</span>
                  <el-tag type="danger" size="small">{{ conflict.device_count }} 台设备冲突</el-tag>
                </div>
              </template>

              <el-table :data="conflict.devices" stripe style="width: 100%">
                <el-table-column prop="name" label="设备名称" width="150" />
                <el-table-column prop="device_type" label="设备类型" width="100">
                  <template #default="scope">
                    {{ deviceTypeOptions[scope.row.device_type] || scope.row.device_type }}
                  </template>
                </el-table-column>
                <el-table-column prop="manufacturer" label="厂商" width="100">
                  <template #default="scope">
                    {{ manufacturerOptions[scope.row.manufacturer] || scope.row.manufacturer }}
                  </template>
                </el-table-column>
                <el-table-column prop="model" label="型号" width="120" />
                <el-table-column prop="datacenter" label="机房" width="100" />
                <el-table-column prop="rack" label="机柜" width="80" />
                <el-table-column prop="position" label="U位" width="60" />
                <el-table-column prop="is_active" label="状态" width="80">
                  <template #default="scope">
                    <el-tag :type="scope.row.is_active ? 'success' : 'info'" size="small">
                      {{ scope.row.is_active ? '在线' : '离线' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" width="150">
                  <template #default="scope">
                    {{ scope.row.created_at ? new Date(scope.row.created_at).toLocaleString() : '-' }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </div>

        <!-- 无冲突时的提示 -->
        <div v-else class="no-conflicts">
          <el-empty description="恭喜！未发现IP地址冲突" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="ipConflictDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="detectIpConflicts" :loading="ipConflictLoading">
            重新检测
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading, ElNotification } from 'element-plus'
import { useRouter } from 'vue-router';
import {
  View, Menu, Timer, ArrowDown, UploadFilled,
  Upload, Star, Delete, Download, SetUp,
  Refresh, Monitor, Connection, Link, House, Grid, Plus,
  InfoFilled, CircleCheck, CircleClose, EditPen,
  Search, Location, Warning
} from '@element-plus/icons-vue';
import api, { getFullDeviceList } from '../utils/api'
import TerminalComponent from '../components/TerminalComponent.vue'
import AddDatacenterDialog from '@/components/rackManagement/dialogs/AddDatacenterDialog.vue';
import Sortable from 'sortablejs';

// 设置Vue响应式数据
const router = useRouter();
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);
const devices = ref([]);
const loading = ref(false);
const exportLoading = ref(false);
const addDatacenterDialogVisible = ref(false);
const loadingDatacenters = ref(false);
const loadingRacks = ref(false);
const datacenters = ref([]);
const racks = ref([]);

// 添加排序相关变量
const sortColumn = ref('');
const sortOrder = ref('');
const defaultSort = ref({ prop: '', order: '' });

const multipleSelection = ref([])

const filterForm = ref({
  name: '',
  ip: '',
  type: '',
  manufacturer: '',
  status: '',
  datacenter: '',
  rack: '',
  serial_number: ''
})

const deviceForm = ref({
  id: null,
  name: '',
  ip_address: '',
  device_type: '',
  manufacturer: '',
  model: '',
  username: '',
  password: '',
  connection_type: 'ssh',
  port: 22,
  protocol: 'cisco_ios', // 添加默认协议类型
  datacenter: '', // 机房
  rack: '',       // 机柜
  position: null, // U位
  serial_number: '' // 序列号
})

// 添加一个静默版的唯一性检查函数，不会在控制台记录错误
const silentCheckUnique = async (field, value, excludeId) => {
  try {
    // 使用fetch API代替axios
    const params = new URLSearchParams({
      field: field,
      value: value,
      exclude_id: excludeId || ''
    });
    
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒超时
    
    const response = await fetch(`http://localhost:5888/api/devices/check-unique?${params.toString()}`, {
      method: 'GET',
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      return { success: false, unique: false, error: 'api_error' };
    }
    
    const data = await response.json();
    return { success: true, unique: data.unique };
  } catch (err) {
    // 完全吞掉错误，不记录任何内容
    return { success: false, unique: false, error: 'network_error' };
  }
};

// 唯一性验证函数
const validateUniqueName = async (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }

  try {
    const result = await silentCheckUnique('name', value, deviceForm.value.id);
    
    if (!result.success) {
      // API请求失败，静默处理
      callback();
      return;
    }
    
    if (!result.unique) {
      callback(new Error('设备名称已存在'));
    } else {
      callback();
    }
  } catch (error) {
    // 不输出错误日志，静默处理
    callback();
  }
};

const validateUniqueIP = async (rule, value, callback) => {
  if (!value) {
    callback();
    return;
  }

  try {
    const result = await silentCheckUnique('ip_address', value, deviceForm.value.id);
    
    if (!result.success) {
      // API请求失败，静默处理
      callback();
      return;
    }
    
    if (!result.unique) {
      callback(new Error('IP地址已被其他设备使用'));
    } else {
      callback();
    }
  } catch (error) {
    // 不输出错误日志，静默处理
    callback();
  }
};

const validateUniqueSerial = async (rule, value, callback) => {
  // 如果为空，由required规则处理，这里不阻止
  if (!value || value.trim() === '') {
    callback();
    return;
  }
  
  // 检查序列号格式
  if (value !== value.trim()) {
    callback(new Error('序列号不能以空格开头或结尾'));
    return;
  }
  
  // 检查是否包含特殊字符
  if (/[^a-zA-Z0-9\-_.]/.test(value)) {
    callback(new Error('序列号只能包含字母、数字、下划线、连字符和点'));
    return;
  }

  try {
    const result = await silentCheckUnique('serial_number', value, deviceForm.value.id);
    
    if (!result.success) {
      // API请求失败，静默处理
      callback();
      return;
    }
    
    if (!result.unique) {
      callback(new Error('序列号已被其他设备使用'));
    } else {
      callback();
    }
  } catch (error) {
    // 不输出错误日志，静默处理
    callback();
  }
};

const deviceRules = {
  name: [
    { required: true, message: '请输入设备名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' },
    { validator: validateUniqueName, trigger: 'blur' }
  ],
  ip_address: [
    { required: true, message: '请输入IP地址', trigger: 'blur' },
    { pattern: /^(\d{1,3}\.){3}\d{1,3}$/, message: '请输入有效的IP地址', trigger: 'blur' },
    { validator: validateUniqueIP, trigger: 'blur' }
  ],
  device_type: [
    { required: true, message: '请选择设备类型', trigger: 'change' }
  ],
  manufacturer: [
    { required: true, message: '请选择厂商', trigger: 'change' }
  ],
  serial_number: [
    { required: true, message: '请输入设备序列号', trigger: 'blur' },
    { validator: validateUniqueSerial, trigger: 'blur' }
  ]
}

const deviceDialogVisible = ref(false)
const dialogType = ref('add')
const deviceFormRef = ref(null)

const autoRefreshEnabled = ref(true);
const autoRefreshInterval = ref(60); // 默认60秒

const refreshInterval = ref(30)
let refreshTimer = null

const manufacturerOptions = ref({
  'huawei': '华为',
  'cisco': '思科',
  'h3c': '华三',
  'ruijie': '锐捷',
  'zte': '中兴',
  'nsfocus': '绿盟',
  'topsec': '天融信',
  'juniper': 'Juniper',
  'maipu': '迈普',
  'fiberhome': '烽火',
  'bell': '贝尔',
  'digitalchina': '神码',
  'sangfor': '深信服',
  'hillstone': '山石网科',
  'venustech': '启明星辰',
  'dptech': '迪普科技',
  'fortinet': '飞塔',
  'paloalto': 'Palo Alto',
  'checkpoint': 'Check Point',
  'f5': 'F5',
  'citrix': 'Citrix',
  'radware': 'Radware',
  'a10': 'A10',
  'array': '阿雷',
  'other': '其他'
})

const deviceTypeOptions = ref({
  'router': '路由器',
  'switch': '交换机',
  'firewall': '防火墙',
  'loadbalancer': '负载均衡',
  'desktop': '台式机',
  'printer': '打印机',
  'laptop': '笔记本电脑',
  'ac_controller': 'AC控制器',
  'access_point': 'AP',
  'server': '服务器'
})

const protocolOptions = {
  'cisco_ios': 'Cisco IOS',
  'cisco_xe': 'Cisco IOS-XE',
  'cisco_xr': 'Cisco IOS-XR',
  'cisco_nxos': 'Cisco NX-OS',
  'huawei': 'Huawei',
  'huawei_vrpv8': 'Huawei VRPv8',
  'hp_comware': 'HP Comware',
  'h3c': 'H3C',
  'h3c_comware': 'H3C Comware',
  'ruijie_os': 'Ruijie OS',
  'fortinet': 'Fortinet',
  'juniper_junos': 'Juniper JUNOS',
  'linux': 'Linux',
  'arista_eos': 'Arista EOS'
}

const statusLoading = ref(false)
const refreshStatus = ref('success') // 添加缺失的状态变量

const savedFilters = ref([]);

// 自定义设备类型和厂商相关
const customTypeDialogVisible = ref(false);
const customManufacturerDialogVisible = ref(false);
const activeCustomTypeTab = ref('add');
const activeCustomManufacturerTab = ref('add');
const customTypeForm = ref({
  key: '',
  label: '',
  isEditing: false,
  originalKey: ''
});
const customManufacturerForm = ref({
  key: '',
  label: '',
  isEditing: false,
  originalKey: ''
});

// IP冲突检测相关
const ipConflictDialogVisible = ref(false);
const ipConflictLoading = ref(false);
const ipConflictData = ref({
  conflicts: [],
  total_conflicts: 0,
  total_devices: 0,
  message: ''
});

const refreshDevicesStatus = async () => {
  if (loading.value) return;

  try {
    loading.value = true;
    
    // 显示进度对话框
    progressDialogVisible.value = true;
    progressDetails.value = [];
    progressSummary.value = "正在刷新设备状态...";
    progressSuccess.value = false;
    progressPercent.value = 10;
    
    const axios = (await import('axios')).default;
    
    // 增加超时时间到60秒，并添加进度提示
    const response = await axios.post('http://localhost:5888/api/devices/refresh-status', {}, {
      timeout: 60000, // 增加到60秒
      onUploadProgress: () => {
        // 请求发送进度
        progressPercent.value = 30;
        progressSummary.value = "正在发送请求...";
      }
    });
    
    // 更新进度
    progressPercent.value = 100;
    progressSuccess.value = true;
    progressSummary.value = response.data.message || '状态刷新成功';
    
    ElMessage.success(response.data.message || '状态刷新成功');
    
    // 重新获取设备列表
    const currentPageBackup = currentPage.value;
    await fetchDevices();
    
    if (devices.value.length === 0 && currentPage.value > 1) {
      currentPage.value = currentPage.value - 1;
      await fetchDevices();
    }
  } catch (error) {
    console.error('刷新设备状态失败', error);
    
    // 更新进度对话框
    progressPercent.value = 100;
    progressSuccess.value = false;
    
    if (error.code === 'ECONNABORTED') {
      progressSummary.value = "刷新设备状态超时，正在尝试备用方法...";
      
      // 尝试使用备用刷新方法
      try {
        const fallbackSuccess = await fallbackRefresh();
        if (fallbackSuccess) {
          progressSuccess.value = true;
          progressSummary.value = "使用备用方法刷新成功";
          return;
        } else {
          progressSummary.value = "备用刷新方法也失败，请检查网络或服务器状态";
        }
      } catch (fallbackError) {
        progressSummary.value = `备用刷新方法失败: ${fallbackError.message}`;
      }
      
      ElMessage.error('刷新设备状态超时，请检查网络或服务器响应');
    } else if (error.response) {
      progressSummary.value = `刷新失败: 服务器返回 ${error.response.status} - ${error.response.statusText}`;
      console.error('错误状态码:', error.response.status);
      console.error('错误详情:', error.response.data);
      ElMessage.error(`刷新失败: ${error.response.status} ${error.response.statusText}`);
    } else {
      progressSummary.value = `刷新设备状态失败: ${error.message}`;
      ElMessage.error('刷新设备状态失败: ' + error.message);
    }
  } finally {
    loading.value = false;
    // 不关闭进度对话框，让用户可以看到结果
  }
}

// 添加备用刷新函数
const fallbackRefresh = async () => {
  try {
    const axios = (await import('axios')).default;
    
    console.log('使用备用刷新方法...');
    
    // 更新进度对话框
    progressPercent.value = 50;
    progressSummary.value = "正在尝试备用刷新方法...";
    
    // 使用批量ping的方式代替全量刷新
    // 先获取所有设备ID
    const devicesResponse = await axios.get('http://localhost:5888/api/direct-devices', {
      timeout: 10000
    });
    
    if (!devicesResponse.data || !Array.isArray(devicesResponse.data)) {
      throw new Error('获取设备列表失败');
    }
    
    // 获取设备ID列表
    const deviceIds = devicesResponse.data.map(device => device.id);
    
    // 如果设备数量过多，分批处理
    const batchSize = 20;
    let updatedCount = 0;
    
    progressSummary.value = `正在分批刷新 ${deviceIds.length} 台设备状态...`;
    
    // 分批处理设备刷新
    for (let i = 0; i < deviceIds.length; i += batchSize) {
      const batchIds = deviceIds.slice(i, i + batchSize);
      progressPercent.value = 50 + Math.floor((i / deviceIds.length) * 40);
      
      try {
        // 使用批量ping接口
        const batchResponse = await axios.post('http://localhost:5888/api/devices/batch-ping', batchIds, {
          timeout: 15000
        });
        
        if (batchResponse.data) {
          // 计算状态变化的设备数
          const changedDevices = Object.values(batchResponse.data).filter(
            device => device.status === "up" || device.status === "down"
          );
          updatedCount += changedDevices.length;
        }
      } catch (batchError) {
        console.warn(`批次 ${i}/${deviceIds.length} 刷新失败:`, batchError);
        // 继续处理下一批，不中断整个过程
      }
    }
    
    progressPercent.value = 90;
    progressSummary.value = "正在更新设备列表...";
    
    // 刷新完成后重新获取设备列表
    await fetchDevices();
    
    progressPercent.value = 100;
    ElMessage.success(`使用备用方法刷新成功，处理了 ${deviceIds.length} 台设备`);
    return true;
  } catch (error) {
    console.error('备用刷新也失败:', error);
    progressSummary.value = `备用刷新失败: ${error.message}`;
    return false;
  }
};

// 修改原刷新函数，加入备用策略
const fetchDevicesStatus = async () => {
  try {
    // 取消之前的请求（如果有）
    if (statusRefreshSource) {
      statusRefreshSource.cancel('新的刷新请求已发起');
    }
    
    // 创建新的取消令牌
    const axios = (await import('axios')).default;
    const CancelToken = axios.CancelToken;
    statusRefreshSource = CancelToken.source();
    
    // 更新状态
    refreshStatus.value = 'loading';
    
    // 显示小型加载提示
    const loadingInstance = ElMessage({
      message: '正在刷新设备状态...',
      type: 'info',
      duration: 0
    });
    
    // 发送刷新请求，增加超时设置和取消令牌
    console.log('刷新设备状态...');
    const response = await axios.post('http://localhost:5888/api/devices/refresh-status', {}, {
      timeout: 30000, // 增加到30秒超时
      cancelToken: statusRefreshSource.token,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    // 关闭加载提示
    loadingInstance.close();
    
    console.log('设备状态刷新响应:', response.data);
    
    // 如果刷新成功，重新获取设备列表
    if (response.data && (response.data.status === 'success' || response.data.updated >= 0)) {
      ElMessage.success(`成功刷新设备状态，${response.data.updated} 台设备状态发生变化`);
      await fetchDevices(); // 重新获取设备列表
      refreshStatus.value = 'success';
    } else {
      ElMessage.warning('设备状态刷新未返回预期结果');
      console.warn('预期外的刷新响应:', response.data);
      refreshStatus.value = 'warning';
    }
  } catch (error) {
    // 判断是否为取消请求导致的错误
    const axios = (await import('axios')).default;
    if (axios.isCancel(error)) {
      console.log('请求被取消:', error.message);
      refreshStatus.value = 'warning';
      return; // 不显示错误消息
    }
    
    console.error('刷新设备状态失败:', error);
    
    // 根据不同的错误类型提供不同的错误消息
    if (error.code === 'ECONNABORTED') {
      ElMessage.warning('刷新设备状态超时，正在尝试备用方法...');
      
      // 尝试备用刷新方法
      console.log('主刷新方法超时，尝试备用方法...');
      const fallbackSuccess = await fallbackRefresh();
      if (fallbackSuccess) {
        refreshStatus.value = 'success';
        return;
      } else {
        ElMessage.error('刷新设备状态失败，备用方法也失败');
      }
    } else if (error.response) {
      ElMessage.error(`刷新状态失败: 服务器返回 ${error.response.status} - ${error.response.statusText}`);
    } else {
      ElMessage.error(`刷新设备状态失败: ${error.message || '未知错误'}`);
    }
    
    // 更新UI显示错误状态
    refreshStatus.value = 'error';
  } finally {
    statusRefreshSource = null;
  }
};

const matchIpAddress = (deviceIp, searchIp) => {
  if (!searchIp) return true;
  
  if (/^(\d{1,3}\.){3}\d{1,3}$/.test(searchIp) && deviceIp === searchIp) {
    return true;
  }
  
  if (deviceIp.includes(searchIp)) {
    return true;
  }
  
  const searchParts = searchIp.split('.');
  if (searchParts.length > 1) {
    const deviceParts = deviceIp.split('.');
    
    outer: for (let i = 0; i <= deviceParts.length - searchParts.length; i++) {
      for (let j = 0; j < searchParts.length; j++) {
        if (deviceParts[i + j] !== searchParts[j]) {
          continue outer;
        }
      }
      return true;
    }
  }
  
  return false;
};

const matchDeviceName = (deviceName, searchName) => {
  if (!searchName) return true;
  
  return deviceName.toLowerCase().includes(searchName.toLowerCase());
};

// 处理表格排序变化
const handleSortChange = ({ column, prop, order }) => {
  console.log('排序变化:', { prop, order });
  sortColumn.value = prop;
  sortOrder.value = order;
  
  // 更新排序信息提示
  updateSortingInfo();
  
  // 重新获取数据
  fetchDevices();
};

// 重置排序
const resetSorting = () => {
  sortColumn.value = '';
  sortOrder.value = '';
  defaultSort.value = { prop: '', order: '' };
  updateSortingInfo();
  fetchDevices();
};

// 更新排序信息提示
const updateSortingInfo = () => {
  if (!sortColumn.value || !sortOrder.value) {
    // 默认排序提示（按在线状态排序）
    sortingInfoText.value = '设备已按状态排序：<span class="status-online">在线设备</span>优先显示在前面，<span class="status-offline">离线设备</span>显示在后面';
    return;
  }
  
  // 获取列名称
  let columnName = '';
  columnOptions.forEach(opt => {
    if (opt.key === 'status' && sortColumn.value === 'is_active') {
      columnName = opt.label;
    } else if (opt.key === sortColumn.value) {
      columnName = opt.label;
    }
  });
  
  // 排序方向
  const direction = sortOrder.value === 'ascending' ? '升序' : '降序';
  
  // 更新排序提示
  sortingInfoText.value = `设备已按<strong>${columnName}</strong>进行<strong>${direction}</strong>排序`;
};

// 添加排序信息文本变量
const sortingInfoText = ref('设备已按状态排序：<span class="status-online">在线设备</span>优先显示在前面，<span class="status-offline">离线设备</span>显示在后面');

// 修改获取设备的方法，直接使用完整API
const fetchDevices = async () => {
  try {
    loading.value = true;
    
    // 使用新的API函数获取完整设备列表
    const allDevicesData = await getFullDeviceList();
    console.log('获取到完整设备列表:', allDevicesData);
    
    // 保存所有设备到临时数组
    let filteredDevices = allDevicesData;
    
    // 应用筛选
    // 筛选名称
    if (filterForm.value.name) {
      filteredDevices = filteredDevices.filter(device => 
        device.name && device.name.toLowerCase().includes(filterForm.value.name.toLowerCase())
      );
    }
    
    // 筛选IP
    if (filterForm.value.ip) {
      filteredDevices = filteredDevices.filter(device => 
        device.ip_address && matchIpAddress(device.ip_address, filterForm.value.ip)
      );
    }
    
    // 筛选设备类型
    if (filterForm.value.type) {
      filteredDevices = filteredDevices.filter(device => 
        device.device_type === filterForm.value.type
      );
    }
    
    // 筛选厂商
    if (filterForm.value.manufacturer) {
      filteredDevices = filteredDevices.filter(device => 
        device.manufacturer === filterForm.value.manufacturer
      );
    }
    
    // 筛选状态
    if (filterForm.value.status) {
      const isActive = filterForm.value.status === 'active';
      filteredDevices = filteredDevices.filter(device => 
        device.is_active === isActive
      );
    }
    
    // 筛选机房
    if (filterForm.value.datacenter) {
      filteredDevices = filteredDevices.filter(device => 
        device.datacenter && device.datacenter === filterForm.value.datacenter
      );
    }
    
    // 筛选机柜
    if (filterForm.value.rack) {
      filteredDevices = filteredDevices.filter(device => 
        device.rack && device.rack === filterForm.value.rack
      );
    }
    
    // 筛选序列号
    if (filterForm.value.serial_number) {
      filteredDevices = filteredDevices.filter(device => 
        device.serial_number && device.serial_number.toLowerCase().includes(filterForm.value.serial_number.toLowerCase())
      );
    }
    
    // 如果有自定义排序，则应用自定义排序
    if (sortColumn.value && sortOrder.value) {
      filteredDevices.sort((a, b) => {
        // 处理序号列的特殊情况
        if (sortColumn.value === 'index') {
          // 序号列使用数组索引作为排序依据
          return sortOrder.value === 'ascending' ? 1 : -1;
        }
          
        let aValue = a[sortColumn.value];
        let bValue = b[sortColumn.value];
        
        // 处理数值型字段
        if (sortColumn.value === 'cpu_usage' || sortColumn.value === 'memory_usage' || 
            sortColumn.value === 'port' || sortColumn.value === 'position') {
          aValue = parseFloat(aValue) || 0;
          bValue = parseFloat(bValue) || 0;
        }
        
        // 处理字符串字段
        if (typeof aValue === 'string' && typeof bValue === 'string') {
          if (sortOrder.value === 'ascending') {
            return aValue.localeCompare(bValue);
          } else {
            return bValue.localeCompare(aValue);
          }
        }
        
        // 处理布尔或数值字段
        if (sortOrder.value === 'ascending') {
          return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        } else {
          return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        }
      });
    } else {
      // 按在线状态排序：在线设备优先显示
      filteredDevices.sort((a, b) => {
        // 首先按在线状态排序（在线的排在前面）
        if (a.is_active && !b.is_active) return -1;
        if (!a.is_active && b.is_active) return 1;
        
        // 如果在线状态相同，可以添加其他排序条件，例如按名称排序
        return a.name?.localeCompare(b.name || '') || 0;
      });
    }
    
    // 保存总记录数
    total.value = filteredDevices.length;
    
    // 应用分页
    const skip = (currentPage.value - 1) * pageSize.value;
    const limit = pageSize.value;
    
    // 分页结果
    devices.value = filteredDevices.slice(skip, skip + limit);
    
    // 更新成功消息
    if (isFiltering()) {
      ElMessage.success(`筛选成功: 获取到 ${filteredDevices.length} 条符合条件的记录`);
    } else {
      ElNotification({
        title: '加载成功',
        message: `成功加载 ${devices.value.length} 条记录 (第 ${currentPage.value}/${Math.ceil(total.value/pageSize.value)} 页)`,
        type: 'success',
        duration: 2000,
        position: 'bottom-right'
      });
    }
  } catch (error) {
    console.error('获取设备数据失败:', error);
    ElMessage.error(`获取设备数据失败: ${error.message}`);
    
    // 尝试使用旧方法作为备用
    try {
      await fetchAllDevicesAndPaginateLocally();
    } catch (e) {
      console.error('前端分页也失败了:', e);
    }
  } finally {
    loading.value = false;
  }
};

// 添加一个辅助函数，检查是否有活跃筛选条件
const isFiltering = () => {
  return Object.values(filterForm.value).some(val => val !== '');
};

// 改进分页处理函数，确保正确调用fetchDevices
const handleCurrentChange = (newPage) => {
  console.log('页码变更:', newPage);
  currentPage.value = newPage;
  fetchDevices();
};

const handleSizeChange = (newSize) => {
  console.log('每页数量变更:', newSize);
  pageSize.value = newSize;
  currentPage.value = 1; // 重置到第一页
  fetchDevices();
};

// 确保这些函数连接到前端分页逻辑
const handlePrevClick = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchDevices();
  }
};

const handleNextClick = () => {
  if (currentPage.value < Math.ceil(total.value / pageSize.value)) {
    currentPage.value++;
    fetchDevices();
  }
};

watch(() => total.value, (newTotal, oldTotal) => {
  console.log(`设备总数变化: ${oldTotal} -> ${newTotal}`);
  const maxPage = Math.ceil(newTotal / pageSize.value) || 1;
  
  if (currentPage.value > maxPage) {
    console.log(`当前页码(${currentPage.value})超出最大页码(${maxPage})，调整为${maxPage}`);
    currentPage.value = maxPage;
    fetchDevices();
  }
});

const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 处理单行选择
const handleRowSelect = (row) => {
  if (row.isSelected) {
    if (!multipleSelection.value.includes(row)) {
      multipleSelection.value.push(row);
    }
  } else {
    multipleSelection.value = multipleSelection.value.filter(item => item.id !== row.id);
  }
}

const openAddDeviceDialog = async () => {
  dialogType.value = 'add'
  deviceForm.value = {
    id: null,
    name: '',
    ip_address: '',
    device_type: '',
    manufacturer: '',
    model: '',
    username: '',
    password: '',
    is_active: true,
    connection_type: 'ssh',
    port: 22,
    protocol: 'cisco_ios', // Set default protocol
    datacenter: '',  // 机房
    rack: '',        // 机柜
    position: null,  // U位
    serial_number: '', // 序列号
    _portManuallySet: false, // 标记端口是否被手动修改过
    _previousConnectionType: 'ssh' // 记录当前连接方式，用于比较变化
  }
  
  // 确保有机房和机柜数据
  if (datacenters.value.length === 0) {
    await fetchDatacenters();
  }
  
  if (racks.value.length === 0) {
    await fetchRacks();
  }
  
  // 清空已占用位置列表
  occupiedPositions.value = [];
  
  deviceDialogVisible.value = true
}

const handleDeviceDetail = (row) => {
  console.log('设备详情', row);
  currentDevice.value = row;
  deviceDetailVisible.value = true;
  showPassword.value = false; // 每次打开详情时重置密码显示状态
}

const handleDeviceEdit = async (row) => {
  dialogType.value = 'edit'
  deviceForm.value = { ...row }

  // 确保编辑时也有默认值 - 从protocol字段获取连接方式
  if (!deviceForm.value.connection_type && deviceForm.value.protocol) {
    deviceForm.value.connection_type = deviceForm.value.protocol;
  } else if (!deviceForm.value.connection_type) {
    deviceForm.value.connection_type = 'ssh';
  }
  
  // 保存原始端口值（如果有）
  const originalPort = deviceForm.value.port;
  
  // 如果没有端口值，设置默认值
  if (!deviceForm.value.port) {
    deviceForm.value.port = deviceForm.value.connection_type === 'ssh' ? 22 : 23;
  }
  
  // 初始化跟踪变量
  deviceForm.value._previousConnectionType = deviceForm.value.connection_type;
  
  // 检查是否使用默认端口 - 自定义端口应该保持不变
  const isDefaultPort = (deviceForm.value.connection_type === 'ssh' && deviceForm.value.port === 22) || 
                       (deviceForm.value.connection_type === 'telnet' && deviceForm.value.port === 23);
  
  // 如果原始端口存在且与默认端口不同，则标记为手动设置
  deviceForm.value._portManuallySet = originalPort ? !isDefaultPort : false;
  
  console.log('编辑设备:', {
    connection_type: deviceForm.value.connection_type,
    port: deviceForm.value.port,
    isDefaultPort: isDefaultPort,
    portManuallySet: deviceForm.value._portManuallySet
  });
  
  deviceDialogVisible.value = true
  
  // 如果设备有机柜信息，则获取该机柜已占用的位置
  if (row.rack) {
    await fetchOccupiedPositions(row.rack);
  }
}

// 新增确认删除方法
const confirmDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除设备 "${row.name}" 吗？此操作不可撤销。`,
    '删除确认',
    {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning',
      customClass: 'top-right-message-box', // 使用自定义CSS类
      draggable: true
    }
  )
  .then(() => {
    // 用户点击确认，执行删除
    handleDeviceDelete(row);
  })
  .catch(() => {
    // 用户取消删除
    ElMessage({
      type: 'info',
      message: '已取消删除'
    });
  });
};

// 执行删除操作
const handleDeviceDelete = async (row) => {
  // 显示加载提示
  const loadingInstance = ElLoading.service({
    text: '正在删除设备...',
    background: 'rgba(0, 0, 0, 0.7)'
  });
  
  try {
    console.log('开始删除设备:', row.id, row.name);
    
    // 先尝试从机柜管理系统中删除设备
    if (row.datacenter && row.rack) {
      try {
        await deleteDeviceFromRack(row);
        console.log('已从机柜管理系统中删除设备');
      } catch (rackError) {
        console.error('从机柜管理系统中删除设备失败:', rackError);
        // 不阻止主流程，继续删除设备信息
        ElMessage({
          type: 'warning',
          message: `从机柜中删除设备失败，但将继续删除设备记录`
        });
      }
    }
    
    // 使用axios直接发送DELETE请求
    const axios = (await import('axios')).default;
    
    // 直接使用完整URL，避免相对路径问题
    const deleteUrl = `http://localhost:5888/api/devices/${row.id}`;
    console.log('删除URL:', deleteUrl);
    
    const response = await axios.delete(deleteUrl);
    console.log('删除响应:', response.status, response.data);
    
    // 关闭加载提示
    loadingInstance.close();
    
    // 删除成功
    ElMessage({
      type: 'success',
      message: `设备 ${row.name} 已删除`
    });
    
    // 刷新设备列表
    await fetchDevices();
    
  } catch (error) {
    // 关闭加载提示
    loadingInstance.close();
    
    console.error('删除设备失败:', error);
    
    // 处理404等错误
    if (error.response && error.response.status === 404) {
      ElMessage({
        type: 'warning',
        message: `设备不存在或已被删除`
      });
      // 刷新列表以反映最新状态
      await fetchDevices();
    } else {
      // 显示错误信息
      let errorMsg = '删除失败';
      if (error.response && error.response.data) {
        errorMsg += ': ' + (error.response.data.detail || error.response.data.message || JSON.stringify(error.response.data));
      } else if (error.message) {
        errorMsg += ': ' + error.message;
      }
      
      ElMessage({
        type: 'error',
        message: errorMsg
      });
    }
  }
}

// 添加一个本地检查序列号唯一性的函数
const checkSerialNumberLocallyUnique = (serialNumber, currentDeviceId = null) => {
  if (!serialNumber || serialNumber.trim() === '') {
    return true; // 空序列号不做唯一性检查
  }
  
  // 在当前设备列表中查找相同序列号的设备
  const foundDevice = devices.value.find(device => 
    device.serial_number && 
    device.serial_number.toLowerCase() === serialNumber.toLowerCase() && 
    device.id !== currentDeviceId
  );
  
  return !foundDevice; // 如果没找到相同序列号的设备，则是唯一的
};

const submitDeviceForm = async () => {
  if (!deviceFormRef.value) return;
  
  deviceFormRef.value.validate(async (valid) => {
    if (!valid) return false;
    
    try {
      // 本地检查序列号唯一性作为后备验证
      const serialNumber = deviceForm.value.serial_number;
      const deviceId = dialogType.value === 'edit' ? deviceForm.value.id : null;
      
      if (serialNumber && !checkSerialNumberLocallyUnique(serialNumber, deviceId)) {
        try {
          await ElMessageBox.confirm(
            '检测到序列号可能已被其他设备使用，是否仍要继续？',
            '序列号重复警告',
            {
              confirmButtonText: '继续提交',
              cancelButtonText: '返回修改',
              type: 'warning'
            }
          );
          // 用户确认继续，不阻止后续操作
        } catch (e) {
          // 用户取消，中止表单提交
          return;
        }
      }
      
      // 如果选择了机房、机柜和U位，先检查该位置是否已被占用
      if (deviceForm.value.datacenter && deviceForm.value.rack && deviceForm.value.position !== null) {
        const isPositionOccupied = await checkRackPositionOccupied(
          deviceForm.value.rack,
          deviceForm.value.position,
          dialogType.value === 'edit' ? deviceForm.value.id : null
        );
        
        if (isPositionOccupied) {
          ElMessage({
            type: 'error',
            message: '该机柜位置已被占用，请选择其他位置'
          });
          return;
        }
      }
      
      // 确保所有字段都正确设置
      const formData = { ...deviceForm.value };
      
      // 设置protocol为连接方式
      formData.protocol = formData.connection_type;
      
      // 设置远程类型，确保编辑时也会更新
      formData.remote_type = getRemoteType(formData.manufacturer, formData.connection_type);
      formData.netmiko_device_type = formData.remote_type;
      
      // 处理端口值 - 始终确保端口值被发送到后端
      // 如果端口被手动修改过，尊重用户的选择
      if (formData.port !== undefined && formData.port !== null) {
        formData.port = parseInt(formData.port, 10);
      } else {
        // 如果没有端口值，则使用默认值
        formData.port = formData.connection_type === 'ssh' ? 22 : 23;
      }
      
      // 添加调试信息
      console.log(`提交端口值: ${formData.port}, 手动修改标记: ${formData._portManuallySet}`);
      
      // 确保port有值
      if (formData.port === undefined || formData.port === null) {
        formData.port = formData.connection_type === 'ssh' ? 22 : 23;
      }
      
      // 添加调试信息
      console.log(`提交的端口值: ${formData.port}, 连接方式: ${formData.connection_type}`);
      
      // 确保新增的机房、机柜、U位字段存在，即使是空值也要发送
      formData.datacenter = formData.datacenter || '';
      formData.rack = formData.rack || '';
      formData.position = formData.position === null ? null : formData.position;
      
      // 调试信息
      console.log('提交到后端的数据:', JSON.stringify(formData));
      
      let response;
      
      if (dialogType.value === 'add') {
        response = await api.post('/', formData);
        ElMessage.success('设备添加成功');
        console.log('添加设备响应:', response.data);
        
        // 如果设备有机房、机柜和U位信息，则同时添加到机柜管理系统
        if (formData.datacenter && formData.rack && formData.position !== null) {
          try {
            await addDeviceToRack(response.data, formData);
          } catch (rackError) {
            console.error('添加设备到机柜失败:', rackError);
            ElMessage.warning(`设备已创建，但添加到机柜失败: ${rackError.message}`);
          }
        }
      } else {
        response = await api.put(`/${formData.id}`, formData);
        ElMessage.success(`设备 ${formData.name} 已更新`);
        console.log('更新设备响应:', response.data);
        
        // 如果设备有机房、机柜和U位信息，则同时更新机柜管理系统中的设备
        if (formData.datacenter && formData.rack && formData.position !== null) {
          try {
            await updateDeviceInRack(response.data, formData);
          } catch (rackError) {
            console.error('更新机柜中的设备失败:', rackError);
            ElMessage.warning(`设备已更新，但更新机柜信息失败: ${rackError.message}`);
          }
        }
      }
      
      deviceDialogVisible.value = false;
      fetchDevices();
    } catch (error) {
      console.error('保存设备失败', error);
      let errorMsg = '保存设备失败: ';
      
      if (error.response?.data?.detail) {
        errorMsg += error.response.data.detail;
        console.log('错误详情:', error.response.data);
      } else {
        errorMsg += error.message;
      }
      
      ElMessage.error(errorMsg);
    }
  });
}

const getDeviceTypeLabel = (type) => {
  return deviceTypeOptions.value[type] || type
}

const getDeviceTypeTag = (type) => {
  const tags = {
    'router': 'warning',
    'switch': 'success',
    'firewall': 'danger',
    'loadbalancer': 'info',
    'desktop': 'primary',
    'printer': 'info',
    'laptop': 'primary',
    'ac_controller': 'warning',
    'access_point': 'success',
    'server': 'danger'
  }
  return tags[type] || ''
}

onMounted(async () => {
  // 加载自定义设备类型和厂商选项
  loadCustomOptions();

  // 先检查 HTTP 头和连接
  await checkHttpHeaders();

  // 获取机房和机柜数据
  await refreshDatacentersAndRacks();
  
  // 然后进行简单数据测试
  const data = await testSimpleData();
  
  if (data && data.length > 0) {
    console.log("数据获取成功，继续初始化...");
    setFilterFromQueryParams();
    startAutoRefresh();
    loadFiltersFromLocalStorage();
    
    // 初始化分页
    currentPage.value = 1;
    await fetchDevices();
  } else {
    console.log("数据获取失败，尝试其他方法...");
    try {
      // 尝试获取后端路由信息
      const axios = (await import('axios')).default;
      const routerCheck = await axios.get('http://localhost:5888/api/router-check');
      console.log("路由检查结果:", routerCheck.data);
      
      // 尝试获取后端数据库状态
      const dbCheck = await axios.get('http://localhost:5888/api/db-check');
      console.log("数据库检查结果:", dbCheck.data);
      
      if (dbCheck.data.device_count > 0) {
        ElMessage.warning(`数据库中有 ${dbCheck.data.device_count} 台设备，但获取数据失败`);
        console.log("数据库中的设备示例:", dbCheck.data.device_samples);
      }
    } catch (error) {
      console.error("其他诊断方法也失败:", error);
    }
  }
  
  if (autoRefreshEnabled.value) {
    startCountdown();
  }
  
  try {
    const savedColumns = localStorage.getItem('deviceListColumns');
    if (savedColumns) {
      const parsedColumns = JSON.parse(savedColumns);
      // 确保有至少一列可见
      if (Object.values(parsedColumns).some(v => v)) {
        visibleColumns.value = parsedColumns;
      }
    }
    
    // 确保tempVisibleColumns有初始值
    tempVisibleColumns.value = JSON.parse(JSON.stringify(visibleColumns.value));
  } catch (error) {
    console.error('加载列设置失败:', error);
  }
  
  // 其余挂载代码...
});

onBeforeUnmount(() => {
  stopAutoRefresh()
  stopCountdown()
})

// 改进自动刷新函数
const startAutoRefresh = () => {
  if (refreshTimer) {
    clearInterval(refreshTimer);
  }
  
  const interval = autoRefreshInterval.value * 1000; // 确保这里使用 autoRefreshInterval
  
  refreshTimer = setInterval(async () => {
    // 只有在页面可见、启用自动刷新且非加载状态下才刷新
    if (document.visibilityState === 'visible' && 
        autoRefreshEnabled.value && 
        !loading.value) {
      await manualRefresh();
    }
  }, interval);
  
  console.log(`自动刷新已启动，间隔 ${autoRefreshInterval.value} 秒`);
};

// 手动刷新函数
const manualRefresh = async () => {
  try {
    refreshStatus.value = 'loading';
    await fetchDevicesStatus();
    refreshStatus.value = 'success';
  } catch (e) {
    console.error('手动刷新出错:', e);
    refreshStatus.value = 'error';
  }
};

const stopAutoRefresh = (event) => {
  if (event) {
    event.stopPropagation();
  }
  
  autoRefreshEnabled.value = false;
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
  
  ElNotification({
    title: '自动刷新',
    // message: '已关闭自动刷新',
    type: 'info',
    duration: 3000,
    position: 'bottom-right'
  });
  return false;
}

const customIntervalDialogVisible = ref(false)
const customInterval = ref(30)

// 处理终端显示方式设置
const handleTerminalPreference = (preference) => {
  if (preference === 'embed' || preference === 'tab') {
    saveTerminalPreference(preference);
  }
};

const handleRefreshCommand = (command) => {
  if (command === 'toggle') {
    toggleAutoRefresh();
    return;
  }
  
  if (command === 'custom') {
    openCustomIntervalDialog();
    return;
  }
  
  const seconds = parseInt(command);
  if (!isNaN(seconds)) {
    changeRefreshInterval(seconds);
  }
}

// 在响应式数据部分添加倒计时变量
const countdownSeconds = ref(0);
let countdownTimer = null;

// 添加开始倒计时函数
const startCountdown = () => {
  // 清除现有倒计时
  if (countdownTimer) {
    clearInterval(countdownTimer);
  }
  
  // 初始化倒计时为选定的间隔时间
  countdownSeconds.value = autoRefreshInterval.value;
  
  // 设置1秒的计时器来更新倒计时
  countdownTimer = setInterval(() => {
    if (countdownSeconds.value > 0) {
      countdownSeconds.value -= 1;
    } else {
      // 当倒计时到达0时，重置为间隔时间
      countdownSeconds.value = autoRefreshInterval.value;
    }
  }, 1000);
};

// 停止倒计时
const stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
  countdownSeconds.value = 0;
};

// 修改toggleAutoRefresh函数以包含倒计时控制
const toggleAutoRefresh = () => {
  autoRefreshEnabled.value = !autoRefreshEnabled.value;
  if (autoRefreshEnabled.value) {
    startAutoRefresh();
    startCountdown();
    ElMessage.success(`已启用自动刷新（${autoRefreshInterval.value}秒）`);
  } else {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    stopCountdown();
    ElMessage.info('已禁用自动刷新');
  }
};

const openCustomIntervalDialog = () => {
  customInterval.value = refreshInterval.value;
  customIntervalDialogVisible.value = true;
}

const applyCustomInterval = () => {
  customIntervalDialogVisible.value = false;
  
  if (customInterval.value < 10) {
    customInterval.value = 10;
    ElMessage.warning('最小刷新间隔已设置为10秒');
  }
  
  changeRefreshInterval(customInterval.value);
  // changeRefreshInterval函数现在会同时处理倒计时
};

// 修改changeRefreshInterval函数，确保倒计时也被更新
const changeRefreshInterval = (seconds) => {
  if (typeof seconds !== 'number') {
    return;
  }
  
  refreshInterval.value = seconds;
  autoRefreshInterval.value = seconds;
  
  // 重新启动自动刷新
  startAutoRefresh();
  
  // 如果自动刷新已启用，也重新启动倒计时
  if (autoRefreshEnabled.value) {
    startCountdown();
  }
  
  if (!autoRefreshEnabled.value) {
    autoRefreshEnabled.value = true;
    ElMessage.success(`已启用自动刷新，间隔 ${seconds} 秒`);
  } else {
    ElMessage.success(`已更改刷新间隔为 ${seconds} 秒`);
  }
};

const debounce = (fn, delay) => {
  let timer = null;
  return function() {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, arguments);
    }, delay);
  };
};

const debouncedSearch = debounce(() => {
  currentPage.value = 1;
  fetchDevices();
}, 300);

watch(
  () => filterForm.value.name,
  (newVal) => {
    console.log('设备名称变化:', newVal);
    debouncedSearch();
  },
  { immediate: true, flush: 'post' }
);

watch(
  () => filterForm.value.ip,
  (newVal) => {
    console.log('IP地址变化:', newVal);
    debouncedSearch();
  },
  { immediate: true, flush: 'post' }
);

watch(() => filterForm.value.type, debouncedSearch);
watch(() => filterForm.value.manufacturer, debouncedSearch);
watch(() => filterForm.value.status, debouncedSearch);
watch(() => filterForm.value.datacenter, debouncedSearch);
watch(() => filterForm.value.rack, debouncedSearch);
watch(() => filterForm.value.serial_number, debouncedSearch);

const handleNameInput = (val) => {
  console.log('设备名称输入:', val);
  nextTick(() => {
    debouncedSearch();
  });
};

const handleIpInput = (val) => {
  console.log('IP地址输入:', val);
  
  if (!val || /^[\d\.]+$/.test(val)) {
    nextTick(() => {
      debouncedSearch();
    });
  }
};

const handleSerialInput = (val) => {
  console.log('序列号输入:', val);
  nextTick(() => {
    debouncedSearch();
  });
};

// 处理机房筛选变化
const handleDatacenterFilterChange = (value) => {
  // 如果机房变了，清空机柜选择
  if (value !== filterForm.value.datacenter) {
    filterForm.value.rack = '';
  }
  // 执行搜索
  debouncedSearch();
};

const setFilterFromQueryParams = async () => {
  const urlParams = new URLSearchParams(window.location.search);
  const manufacturer = urlParams.get('manufacturer');
  const autoLogin = urlParams.get('auto_login');
  const deviceId = urlParams.get('device_id');
  const deviceName = urlParams.get('device_name');
  const managementIp = urlParams.get('management_ip');
  const loginIp = urlParams.get('login_ip');
  const fromPage = urlParams.get('from');
  const search = urlParams.get('search');

  if (manufacturer) {
    filterForm.value.manufacturer = manufacturer;
  }

  // 如果有搜索参数，设置搜索条件
  if (search) {
    filterForm.value.search = search;
  }

  // 处理自动登录
  if (autoLogin === 'true' && (deviceId || deviceName || managementIp)) {
    console.log('检测到自动登录请求:', {
      deviceId,
      deviceName,
      managementIp,
      loginIp,
      fromPage
    });

    // 等待设备列表加载完成后再执行自动登录
    setTimeout(async () => {
      await handleAutoLogin({
        deviceId,
        deviceName,
        managementIp,
        loginIp,
        fromPage
      });
    }, 2000); // 等待2秒确保设备列表已加载
  }
};

// 处理自动登录
const handleAutoLogin = async (params) => {
  try {
    const { deviceId, deviceName, managementIp, loginIp, fromPage } = params;

    // 首先尝试根据设备ID查找设备
    let targetDevice = null;

    if (deviceId) {
      targetDevice = devices.value.find(device => device.id == deviceId);
    }

    // 如果没有找到，尝试根据设备名称查找
    if (!targetDevice && deviceName) {
      targetDevice = devices.value.find(device =>
        device.name === deviceName || device.name.includes(deviceName)
      );
    }

    // 如果还没有找到，尝试根据IP地址查找
    if (!targetDevice && (managementIp || loginIp)) {
      const searchIp = loginIp || managementIp;
      targetDevice = devices.value.find(device => device.ip_address === searchIp);
    }

    if (targetDevice) {
      ElMessage.info(`找到目标设备: ${targetDevice.name} (${targetDevice.ip_address})`);

      // 显示确认对话框
      const confirmResult = await ElMessageBox.confirm(
        `检测到来自${fromPage === 'ip_conflict_detection' ? 'IP冲突检测' : '其他页面'}的自动登录请求\n\n` +
        `目标设备信息：\n` +
        `• 设备名称：${targetDevice.name}\n` +
        `• IP地址：${targetDevice.ip_address}\n` +
        `• 设备类型：${getDeviceTypeLabel(targetDevice.device_type)}\n` +
        `• 厂商：${getManufacturerLabel(targetDevice.manufacturer)}\n` +
        `• 连接方式：${targetDevice.protocol === 'ssh' ? 'SSH' : 'Telnet'}\n` +
        `• 状态：${targetDevice.is_active ? '在线' : '离线'}\n\n` +
        `确定要立即登录到此设备吗？`,
        '自动登录确认',
        {
          confirmButtonText: '立即登录',
          cancelButtonText: '取消',
          type: 'info',
          customClass: 'auto-login-confirm'
        }
      );

      if (confirmResult === 'confirm') {
        // 检查设备是否在线
        if (!targetDevice.is_active) {
          ElMessage.warning('目标设备当前离线，无法建立连接');
          return;
        }

        // 执行登录
        ElMessage.success(`正在自动登录到设备: ${targetDevice.name}`);
        await handleDeviceLogin(targetDevice);

        // 清除URL参数，避免重复触发
        const newUrl = window.location.pathname + window.location.search.replace(/[?&]auto_login=true[^&]*(&[^&]*)*/, '');
        window.history.replaceState({}, '', newUrl);

      } else {
        ElMessage.info('已取消自动登录');
      }
    } else {
      ElMessage.warning(`未找到匹配的设备 (ID: ${deviceId}, 名称: ${deviceName}, IP: ${managementIp || loginIp})`);

      // 如果有搜索条件，执行搜索以帮助用户找到设备
      if (deviceName) {
        filterForm.value.search = deviceName;
        debouncedSearch();
        ElMessage.info(`已为您搜索设备: ${deviceName}`);
      }
    }
  } catch (error) {
    if (error === 'cancel') {
      ElMessage.info('已取消自动登录');
    } else {
      console.error('自动登录处理失败:', error);
      ElMessage.error(`自动登录失败: ${error.message || '未知错误'}`);
    }
  }
};

const getManufacturerLabel = (code) => {
  return manufacturerOptions.value[code] || code;
};

const getManufacturerCodeByLabel = (label) => {
  for (const [code, name] of Object.entries(manufacturerOptions.value)) {
    if (name === label) {
      return code;
    }
  }
  return null;
};

const importDialogVisible = ref(false)
const uploadRef = ref(null)
const importFile = ref(null)
const fileList = ref([])
const importLoading = ref(false)

const openImportDialog = () => {
  importDialogVisible.value = true
  fileList.value = []
  importFile.value = null
}

const handleFileChange = (uploadFile) => {
  importFile.value = uploadFile;
  fileList.value = uploadRef.value?.uploadFiles || [uploadFile];
  
  console.log('文件已选择:', uploadFile.name);
};

const handleFileRemove = () => {
  importFile.value = null
  fileList.value = []
}

const beforeUpload = (file) => {
  const isValid = /\.(xlsx|xls|csv)$/.test(file.name.toLowerCase());
  if (!isValid) {
    ElMessage.error('只支持Excel或CSV文件格式');
    return false;
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5;
  if (!isLt5M) {
    ElMessage.error('文件大小不能超过5MB!');
    return false;
  }
  
  return false;
};

const handleBatchImport = async () => {
  if (!importFile.value) {
    ElMessage.warning('请先选择要导入的文件');
    return;
  }
  
  importLoading.value = true;
  try {
    const file = importFile.value.raw || importFile.value;
    
    if (!file || !(file instanceof Blob)) {
      throw new Error('无效的文件对象');
    }
    
    const reader = new FileReader();
    
    reader.onload = async (e) => {
      try {
        const csvContent = e.target.result;
        
        const devices = parseCSV(csvContent);
        
        if (devices.length === 0) {
          ElMessage.warning('CSV文件中没有找到有效的设备数据');
          importLoading.value = false;
          return;
        }
        
        const { action } = await ElMessageBox.confirm(
          `已从CSV文件中解析出 ${devices.length} 台设备，是否批量导入？`,
          '批量导入确认',
          {
            confirmButtonText: '开始导入',
            cancelButtonText: '仅预览',
            distinguishCancelAndClose: true,
            type: 'info'
          }
        ).then(res => ({ action: 'import' }))
        .catch(action => {
          return { action: action === 'cancel' ? 'preview' : 'close' };
        });
        
        if (action === 'preview') {
          showPreviewDialog(devices);
        } else if (action === 'import') {
          await batchImportDevices(devices);
        } else {
          importLoading.value = false;
          importDialogVisible.value = false;
        }
      } catch (error) {
        console.error('处理文件失败:', error);
        ElMessage.error(`文件处理失败: ${error.message || '未知错误'}`);
        importLoading.value = false;
      }
    };
    
    reader.onerror = () => {
      ElMessage.error('文件读取出错');
      importLoading.value = false;
    };
    
    reader.readAsText(file);
  } catch (error) {
    console.error('批量导入设备失败:', error);
    ElMessage.error(`导入失败: ${error.message || '未知错误'}`);
    importLoading.value = false;
  }
};

const parseCSV = (csvContent) => {
  try {
    const lines = csvContent.split(/\r\n|\n/);
    const headers = lines[0].split(',').map(h => h.trim());
    
    const requiredFields = ['name', 'ip_address', 'device_type', 'manufacturer'];
    const missingFields = requiredFields.filter(field => !headers.includes(field));
    
    if (missingFields.length > 0) {
      ElMessage.error(`CSV文件缺少必需字段: ${missingFields.join(', ')}`);
      return [];
    }
    
    const devices = [];
    for (let i = 1; i < lines.length; i++) {
      if (!lines[i].trim()) continue;
      
      const values = lines[i].split(',');
      const device = {};
      
      headers.forEach((header, index) => {
        if (index < values.length) {
          const value = values[index].replace(/^"(.*)"$/, '$1').trim();
          
          // 处理特殊类型字段
          if (header === 'position' && value !== '') {
            // 将U位转换为数字
            device[header] = parseInt(value, 10);
          } else if (header === 'port' && value !== '') {
            // 将端口转换为数字
            device[header] = parseInt(value, 10);
          } else {
            device[header] = value;
          }
        }
      });
      
      if (requiredFields.every(field => device[field])) {
        // 确保位置字段存在且格式正确
        if (device.position === '' || isNaN(device.position)) {
          device.position = null;
        }
        
        devices.push(device);
      }
    }
    
    console.log('解析CSV结果:', devices);
    return devices;
  } catch (error) {
    console.error('解析CSV失败:', error);
    ElMessage.error('无法解析CSV文件');
    return [];
  }
};

const batchImportDevices = async (devices) => {
  if (!devices || devices.length === 0) {
    ElMessage.warning('没有可导入的设备');
    importLoading.value = false;
    importDialogVisible.value = false;
    return;
  }
  
  try {
    ElMessage.info(`开始导入 ${devices.length} 台设备，请稍候...`);
    
    let successCount = 0;
    let failCount = 0;
    const failures = [];
    
    const importPromises = devices.map(async (device, index) => {
      try {
        if (index > 0 && index % 5 === 0) {
          await new Promise(resolve => setTimeout(resolve, 300));
        }
        
        // 创建设备数据对象
        const deviceData = {
          name: device.name,
          ip_address: device.ip_address,
          device_type: device.device_type,
          manufacturer: device.manufacturer,
          model: device.model || '',
          username: device.username || '',
          password: device.password || '',
          datacenter: device.datacenter || '',
          rack: device.rack || '',
          position: device.position || null,
          connection_type: device.connection_type || 'ssh',
          port: device.port || 22
        };
        
        // 添加设备到设备管理系统
        const response = await api.post('/', deviceData);
        
        // 如果设备有机房、机柜和U位信息，则同时添加到机柜管理系统
        if (deviceData.datacenter && deviceData.rack && deviceData.position !== null) {
          try {
            await addDeviceToRack(response.data, deviceData);
          } catch (rackError) {
            console.error(`设备 ${device.name} 添加到机柜失败:`, rackError);
            // 不影响主流程，仍然算作成功
          }
        }
        
        successCount++;
        return { success: true, device };
      } catch (error) {
        failCount++;
        failures.push({
          device,
          error: error.response?.data?.detail || error.message || '未知错误'
        });
        return { success: false, device, error };
      }
    });
    
    const results = await Promise.all(importPromises);
    
    await fetchDevices();
    
    if (failCount === 0) {
      ElMessage.success(`成功导入 ${successCount} 台设备`);
    } else {
      let failureMsg = `导入结果: ${successCount} 台成功, ${failCount} 台失败`;
      
      if (failures.length > 0) {
        failureMsg += '\n\n失败设备:';
        failures.slice(0, 5).forEach(f => {
          failureMsg += `\n- ${f.device.name} (${f.device.ip_address}): ${f.error}`;
        });
        
        if (failures.length > 5) {
          failureMsg += `\n...及其他 ${failures.length - 5} 台`;
        }
      }
      
      ElMessageBox.alert(failureMsg, '批量导入结果', {
        confirmButtonText: '确定',
        type: 'warning',
        dangerouslyUseHTMLString: true
      });
    }
  } catch (error) {
    console.error('批量导入设备失败:', error);
    ElMessage.error(`批量导入失败: ${error.message || '未知错误'}`);
  } finally {
    importLoading.value = false;
    importDialogVisible.value = false;
  }
};

const parseAndPreviewCSV = (csvContent) => {
  const devices = parseCSV(csvContent);
  
  if (devices.length === 0) {
    ElMessage.warning('CSV文件中没有找到有效的设备数据');
    importLoading.value = false;
    return;
  }
  
  showPreviewDialog(devices);
};

const previewDialogVisible = ref(false);
const previewDevices = ref([]);

const showPreviewDialog = (devices) => {
  previewDevices.value = devices;
  importLoading.value = false;
  importDialogVisible.value = false;
  previewDialogVisible.value = true;
};

const importSingleDevice = async (device) => {
  // 如果设备有机房信息但没有机柜信息，尝试获取机柜列表
  if (device.datacenter && !device.rack && racks.value.length === 0) {
    await fetchRacks();
  }
  
  deviceForm.value = {
    id: null,
    name: device.name || '',
    ip_address: device.ip_address || '',
    device_type: device.device_type || '',
    manufacturer: device.manufacturer || '',
    model: device.model || '',
    username: device.username || '',
    password: device.password || '',
    is_active: true,
    connection_type: device.connection_type || 'ssh',
    port: device.port || 22,
    datacenter: device.datacenter || '',
    rack: device.rack || '',
    position: device.position !== undefined ? device.position : null
  };
  
  previewDialogVisible.value = false;
  dialogType.value = 'add';
  deviceDialogVisible.value = true;
};

const exportSelectedDevices = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一台设备');
    return;
  }
  
  try {
    const selectedDevices = multipleSelection.value;
    
    let csvContent = "设备名称,IP地址,设备类型,厂商,型号,状态,机房,机柜,U位\n";
    
    selectedDevices.forEach(device => {
      const deviceType = getDeviceTypeLabel(device.device_type);
      const manufacturer = manufacturerOptions.value[device.manufacturer] || device.manufacturer;
      const status = device.is_active ? '在线' : '离线';
      
      const escapeCsv = (field) => {
        if (field === null || field === undefined) return '';
        field = String(field);
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      };
      
      csvContent += `${escapeCsv(device.name)},${escapeCsv(device.ip_address)},` +
                   `${escapeCsv(deviceType)},${escapeCsv(manufacturer)},` +
                   `${escapeCsv(device.model)},${escapeCsv(status)},` +
                   `${escapeCsv(device.datacenter || '')},${escapeCsv(device.rack || '')},` +
                   `${escapeCsv(device.position || '')}\n`;
    });
    
    const encodedCsv = new TextEncoder().encode('\uFEFF' + csvContent);
    const blob = new Blob([encodedCsv], { type: 'text/csv;charset=utf-8;' });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', '所选设备列表.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    ElMessage.success(`已成功导出 ${selectedDevices.length} 台设备 (CSV 格式)`);
  } catch (error) {
    console.error('导出选中设备失败:', error);
    ElMessage.error('导出失败');
  }
};

// 导出当前筛选结果
const exportFilteredDevices = () => {
  if (devices.value.length === 0) {
    ElMessage.warning('当前没有设备数据可导出');
    return;
  }

  try {
    const filteredDevices = devices.value;

    let csvContent = "设备名称,IP地址,设备类型,厂商,型号,序列号,状态,连接方式,端口,机房,机柜,U位,CPU使用率,内存使用率,版本信息,创建时间\n";

    filteredDevices.forEach(device => {
      const deviceType = getDeviceTypeLabel(device.device_type);
      const manufacturer = manufacturerOptions.value[device.manufacturer] || device.manufacturer;
      const status = device.is_active ? '在线' : '离线';
      const protocol = device.protocol === 'ssh' ? 'SSH' : 'Telnet';
      const cpuUsage = device.cpu_usage ? `${parseInt(device.cpu_usage)}%` : '';
      const memoryUsage = device.memory_usage ? `${parseInt(device.memory_usage)}%` : '';
      const createdAt = device.created_at ? new Date(device.created_at).toLocaleString() : '';

      const escapeCsv = (field) => {
        if (field === null || field === undefined) return '';
        field = String(field);
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      };

      csvContent += `${escapeCsv(device.name)},${escapeCsv(device.ip_address)},` +
                   `${escapeCsv(deviceType)},${escapeCsv(manufacturer)},` +
                   `${escapeCsv(device.model || '')},${escapeCsv(device.serial_number || '')},` +
                   `${escapeCsv(status)},${escapeCsv(protocol)},${escapeCsv(device.port || '')},` +
                   `${escapeCsv(device.datacenter || '')},${escapeCsv(device.rack || '')},` +
                   `${escapeCsv(device.position || '')},${escapeCsv(cpuUsage)},` +
                   `${escapeCsv(memoryUsage)},${escapeCsv(device.version_info || '')},` +
                   `${escapeCsv(createdAt)}\n`;
    });

    const encodedCsv = new TextEncoder().encode('\uFEFF' + csvContent);
    const blob = new Blob([encodedCsv], { type: 'text/csv;charset=utf-8;' });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', '筛选设备列表.csv');
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success(`已成功导出 ${filteredDevices.length} 台设备 (CSV 格式)`);
  } catch (error) {
    console.error('导出筛选设备失败:', error);
    ElMessage.error('导出失败');
  }
};

// 导出所有设备
const exportAllDevices = async () => {
  exportLoading.value = true;

  try {
    ElMessage.info('正在获取所有设备数据，请稍候...');

    // 获取所有设备数据（不分页）
    const allDevicesData = await getFullDeviceList();

    if (!allDevicesData || allDevicesData.length === 0) {
      ElMessage.warning('没有设备数据可导出');
      return;
    }

    let csvContent = "设备名称,IP地址,设备类型,厂商,型号,序列号,状态,连接方式,端口,机房,机柜,U位,CPU使用率,内存使用率,版本信息,创建时间\n";

    allDevicesData.forEach(device => {
      const deviceType = getDeviceTypeLabel(device.device_type);
      const manufacturer = manufacturerOptions.value[device.manufacturer] || device.manufacturer;
      const status = device.is_active ? '在线' : '离线';
      const protocol = device.protocol === 'ssh' ? 'SSH' : 'Telnet';
      const cpuUsage = device.cpu_usage ? `${parseInt(device.cpu_usage)}%` : '';
      const memoryUsage = device.memory_usage ? `${parseInt(device.memory_usage)}%` : '';
      const createdAt = device.created_at ? new Date(device.created_at).toLocaleString() : '';

      const escapeCsv = (field) => {
        if (field === null || field === undefined) return '';
        field = String(field);
        if (field.includes(',') || field.includes('"') || field.includes('\n')) {
          return `"${field.replace(/"/g, '""')}"`;
        }
        return field;
      };

      csvContent += `${escapeCsv(device.name)},${escapeCsv(device.ip_address)},` +
                   `${escapeCsv(deviceType)},${escapeCsv(manufacturer)},` +
                   `${escapeCsv(device.model || '')},${escapeCsv(device.serial_number || '')},` +
                   `${escapeCsv(status)},${escapeCsv(protocol)},${escapeCsv(device.port || '')},` +
                   `${escapeCsv(device.datacenter || '')},${escapeCsv(device.rack || '')},` +
                   `${escapeCsv(device.position || '')},${escapeCsv(cpuUsage)},` +
                   `${escapeCsv(memoryUsage)},${escapeCsv(device.version_info || '')},` +
                   `${escapeCsv(createdAt)}\n`;
    });

    const encodedCsv = new TextEncoder().encode('\uFEFF' + csvContent);
    const blob = new Blob([encodedCsv], { type: 'text/csv;charset=utf-8;' });

    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);

    // 生成带时间戳的文件名
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '').replace('T', '_');
    link.setAttribute('download', `设备信息导出_${timestamp}.csv`);

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success(`已成功导出 ${allDevicesData.length} 台设备 (CSV 格式)`);
  } catch (error) {
    console.error('导出所有设备失败:', error);
    ElMessage.error('导出失败，请稍后重试');
  } finally {
    exportLoading.value = false;
  }
};

const downloadTemplate = () => {
  try {
    createSimpleTemplate();
  } catch (error) {
    console.error('创建模板失败:', error);
    ElMessage.error('创建模板失败');
  }
};

const createSimpleTemplate = () => {
  const csvContent = "name,ip_address,device_type,manufacturer,model,username,password,connection_type,port,datacenter,rack,position\n" +
    "路由器1,***********,router,huawei,AR1220,admin,Huawei@123,ssh,22,一号机房,A01,10\n" +
    "交换机1,***********,switch,cisco,Catalyst 2960,admin,Cisco@123,ssh,22,一号机房,A01,11\n" +
    "防火墙1,***********54,firewall,h3c,F1000,admin,H3c@123,telnet,23,二号机房,B02,15\n" +
    "服务器1,***********00,server,huawei,RH2288H V3,root,Server@123,ssh,22,一号机房,A01,12\n" +
    "台式机1,***********01,desktop,,,,,,,办公区,C01,\n" +
    "打印机1,***********02,printer,,,,,,,办公区,C02,\n" +
    "AC控制器1,***********0,ac_controller,huawei,AC6605,admin,Huawei@123,ssh,22,一号机房,A02,5\n" +
    "AP1,***********1,access_point,huawei,AP6050DN,admin,Huawei@123,telnet,23,办公区,,";

  const encodedCsv = new TextEncoder().encode('\uFEFF' + csvContent);
  const blob = new Blob([encodedCsv], { type: 'text/csv;charset=utf-8;' });
  
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.setAttribute('download', '设备导入模板.csv');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  window.URL.revokeObjectURL(url);
  
  ElMessage.success('已创建设备导入模板 (CSV 格式)');
};

const importDevicesFromPreview = () => {
  previewDialogVisible.value = false;
  
  batchImportDevices(previewDevices.value);
};

const ensureValidPagination = () => {
  if (devices.value.length === 0 && currentPage.value > 1) {
    currentPage.value--;
    fetchDevices();
    return false;
  }
  return true;
};

const handleBatchDelete = async () => {
  if (multipleSelection.value.length === 0) {
    ElMessage.warning('请至少选择一台设备');
    return;
  }
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${multipleSelection.value.length} 台设备吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );
    
    const deviceIds = multipleSelection.value.map(device => device.id);
    
    // 先从机柜管理系统中删除设备
    const devicesToDeleteFromRack = multipleSelection.value.filter(device => device.datacenter && device.rack);
    if (devicesToDeleteFromRack.length > 0) {
      ElMessage.info(`正在从机柜管理系统中删除 ${devicesToDeleteFromRack.length} 台设备...`);
      
      // 并行处理所有设备的删除
      await Promise.allSettled(
        devicesToDeleteFromRack.map(async (device) => {
          try {
            await deleteDeviceFromRack(device);
            console.log(`已从机柜管理系统中删除设备: ${device.name}`);
          } catch (error) {
            console.error(`从机柜管理系统中删除设备 ${device.name} 失败:`, error);
          }
        })
      );
    }
    
    const loadingMessage = ElMessage({
      message: `正在删除 ${multipleSelection.value.length} 台设备，请稍候...`,
      type: 'info',
      duration: 0
    });
    try {
      // 直接使用逐个删除方法，跳过批量删除API（避免405错误）
      loadingMessage.close();
      console.log('正在使用单个删除方法删除所选设备...');
      await fallbackToIndividualDelete(multipleSelection.value);
    } catch (error) {
      loadingMessage.close();
      console.error('删除设备失败:', error);
      ElMessage.error('删除设备失败，请稍后重试');
    }
  } catch (error) {
    console.log('用户取消批量删除');
  }
};

// 单个删除方法
const fallbackToIndividualDelete = async (devicesToDelete) => {
  ElMessage.info('正在逐个删除设备...');
  
  let successCount = 0;
  let failCount = 0;
  const failures = [];
  
  const loadingMessage = ElMessage({
    message: `正在逐个删除 ${devicesToDelete.length} 台设备，请稍候...`,
    type: 'info',
    duration: 0
  });
  
  try {
    const axios = (await import('axios')).default;
    
    for (const device of devicesToDelete) {
      try {
        // 先尝试从机柜管理系统中删除设备
        if (device.datacenter && device.rack) {
          try {
            await deleteDeviceFromRack(device);
            console.log(`已从机柜管理系统中删除设备: ${device.name}`);
          } catch (rackError) {
            console.error(`从机柜管理系统中删除设备 ${device.name} 失败:`, rackError);
            // 不阻止主流程，继续删除设备信息
          }
        }
        
        const response = await axios.delete(`http://localhost:5888/api/devices/${device.id}`);
        if (response.data.status === 'success') {
          successCount++;
        } else {
          failCount++;
          failures.push({
            name: device.name,
            id: device.id,
            error: response.data.message || '未知错误'
          });
        }
      } catch (error) {
        failCount++;
        failures.push({
          name: device.name,
          id: device.id,
          error: error.response?.data?.detail || error.message || '未知错误'
        });
      }
      
      // 添加短暂延迟，避免请求过快
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    loadingMessage.close();
    
    // 重新获取设备列表
    await fetchDevices();
    
    // 显示删除结果
    // if (failCount === 0) {
    //   ElMessage.success(`成功删除 ${successCount} 台设备`);
    // } else {
    //   let failureMsg = `删除结果: ${successCount} 台成功, ${failCount} 台失败`;
      
    //   if (failures.length > 0) {
    //     failureMsg += '\n\n删除失败的设备:';
    //     failures.slice(0, 5).forEach(f => {
    //       failureMsg += `\n- ${f.name}: ${f.error}`;
    //     });
        
    //     if (failures.length > 5) {
    //       failureMsg += `\n...及其他 ${failures.length - 5} 台设备`;
    //     }
    //   }
      
    //   ElMessageBox.alert(failureMsg, '批量删除结果', {
    //     confirmButtonText: '确定',
    //     type: 'warning'
    //   });
    // }
  } catch (error) {
    loadingMessage.close();
    console.error('备用删除方法也失败:', error);
    ElMessage.error('所有删除尝试均失败，请联系管理员');
  }
};

const saveCurrentFilter = () => {
  const hasFilter = Object.values(filterForm.value).some(val => val !== '');
  
  if (!hasFilter) {
    ElMessage.warning('请先设置筛选条件');
    return;
  }
  
  ElMessageBox.prompt('请为此筛选条件命名', '保存筛选', {
    confirmButtonText: '保存',
    cancelButtonText: '取消',
    inputPlaceholder: '例如：在线路由器、华为设备等',
    inputValidator: (value) => {
      return value.trim() !== '';
    },
    inputErrorMessage: '名称不能为空'
  }).then(({ value }) => {
    const newFilter = {
      id: Date.now().toString(),
      name: value.trim(),
      conditions: { ...filterForm.value }
    };
    
    savedFilters.value.push(newFilter);
    saveFiltersToLocalStorage();
    ElMessage.success(`筛选条件"${newFilter.name}"已保存`);
  }).catch(() => {
    // 用户取消
  });
};

const saveFiltersToLocalStorage = () => {
  try {
    localStorage.setItem('deviceFilters', JSON.stringify(savedFilters.value));
  } catch (error) {
    console.error('保存筛选条件失败:', error);
  }
};

const loadFiltersFromLocalStorage = () => {
  try {
    const saved = localStorage.getItem('deviceFilters');
    if (saved) {
      savedFilters.value = JSON.parse(saved);
    }
  } catch (error) {
    console.error('加载筛选条件失败:', error);
  }
};

const applyFilter = (filter) => {
  filterForm.value = { ...filter.conditions };
  currentPage.value = 1;
  fetchDevices();
  ElMessage.success(`已应用筛选条件"${filter.name}"`);
};

const deleteFilter = (filter, event) => {
  event.stopPropagation();
  
  ElMessageBox.confirm(
    `确定要删除筛选条件"${filter.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    savedFilters.value = savedFilters.value.filter(f => f.id !== filter.id);
    saveFiltersToLocalStorage();
    ElMessage.success(`筛选条件"${filter.name}"已删除`);
  }).catch(() => {
    // 用户取消
  });
};

const testApiConnection = async () => {
  try {
    console.log('尝试API连接测试');
    
    // 1. 首先测试直接访问后端API
    const axios = (await import('axios')).default;
    try {
      console.log('测试直接请求 http://localhost:5888/api/devices/');
      const directResponse = await axios.get('http://localhost:5888/api/devices/');
      console.log('直接API连接成功:', directResponse.data);
    } catch (directError) {
      console.error('直接API请求失败:', directError);
      
      // 尝试测试端点
      try {
        console.log('尝试测试端点 http://localhost:5888/api/devices-test');
        const testResponse = await axios.get('http://localhost:5888/api/devices-test');
        console.log('测试端点成功:', testResponse.data);
      } catch (testError) {
        console.error('测试端点失败:', testError);
      }
    }
    
    // 2. 然后测试使用API封装的方式 - 注意这里不需要 '/devices'
    console.log('测试API封装: api.get("/")');
    const response = await api.get('/');
    console.log('API封装连接成功:', response.data);
    
    ElMessage.success('API连接测试成功');
  } catch (error) {
    console.error('API连接测试失败', error);
    
    let errorMsg = '未知错误';
    if (error.response) {
      errorMsg = `状态码: ${error.response.status}, `;
      
      if (error.response.status === 404) {
        errorMsg += '请求的API路径不存在。请检查配置。';
      } else if (error.response.data && error.response.data.detail) {
        if (Array.isArray(error.response.data.detail)) {
          errorMsg += error.response.data.detail.map(e => {
            const field = e.loc ? e.loc.join('.') : '未知字段';
            return `字段: ${field}, 错误: ${e.msg}`;
          }).join('; ');
        } else {
          errorMsg += error.response.data.detail;
        }
      } else if (error.response.data) {
        errorMsg += JSON.stringify(error.response.data);
      }
    } else if (error.message) {
      errorMsg = error.message;
    }
    
    ElMessage.error(`API连接测试失败: ${errorMsg}`);
  }
};

const testAllApiPaths = async () => {
  try {
    console.log('开始全面API测试...');
    const axios = (await import('axios')).default;
    
    // 测试不同的API端点组合
    const endpoints = [
      'http://localhost:5888/api',
      'http://localhost:5888/api/devices',
      'http://localhost:5888/api/health',
      'http://localhost:5888/devices',
      'http://localhost:5888/',
      'http://localhost:3000/api/devices',
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`测试端点: ${endpoint}`);
        const response = await axios.get(endpoint);
        console.log(`✅ 成功: ${endpoint}`, response.data);
      } catch (error) {
        console.log(`❌ 失败: ${endpoint}`, error.message);
        
        if (error.response) {
          console.log(`  状态: ${error.response.status} ${error.response.statusText}`);
          console.log(`  数据:`, error.response.data);
        }
      }
    }
    
    // 测试实际API实例
    try {
      console.log('测试API实例配置...');
      console.log('baseURL:', api.defaults.baseURL);
      const apiResponse = await api.get('/');
      console.log('✅ API实例根路径访问成功:', apiResponse.data);
    } catch (apiError) {
      console.log('❌ API实例根路径访问失败:', apiError.message);
    }
    
    ElMessage.info('API测试完成，请查看控制台日志');
  } catch (error) {
    console.error('API测试出错:', error);
  }
};

const testDirectAndProxiedConnections = async () => {
  console.log('======= 开始连接测试 =======');
  try {
    const axios = (await import('axios')).default;
    
    // 测试各种组合（添加尾部斜杠）
    const testCases = [
      {name: '直接访问后端API', url: 'http://localhost:5888/api/devices/?skip=0&limit=1'},
      {name: '直接访问后端API根路径', url: 'http://localhost:5888/api/'},
      {name: '直接访问后端API测试端点', url: 'http://localhost:5888/api/test/'},
      {name: '直接访问后端API调试端点', url: 'http://localhost:5888/api/debug/'},
      {name: '通过Vite代理访问API', url: '/api/devices/?skip=0&limit=1'},
      {name: '通过Vite代理访问API根路径', url: '/api/'},
      {name: '通过Vite代理访问API测试端点', url: '/api/test/'},
    ];
    
    for (const test of testCases) {
      try {
        console.log(`测试 ${test.name}: ${test.url}`);
        const response = await axios.get(test.url);
        console.log(`✅ 成功: ${test.url}`, response.status, response.data);
      } catch (error) {
        console.log(`❌ 失败: ${test.url}`, error.message);
        if (error.response) {
          console.log(`  状态: ${error.response.status}`);
          console.log(`  数据:`, error.response.data);
        }
      }
    }
    
    // 测试API实例配置
    try {
      console.log('测试API实例:');
      console.log(' - baseURL:', api.defaults.baseURL);
      const response = await api.get('/test/');
      console.log(' - ✅ 成功:', response.data);
    } catch (error) {
      console.log(' - ❌ 失败:', error.message);
    }
    
  } catch (error) {
    console.error('测试过程出错:', error);
  }
  console.log('======= 连接测试结束 =======');
};

const testBasicConnection = async () => {
  console.log('==== 测试基本API连接 ====');
  const axios = (await import('axios')).default;
  
  try {
    // 添加尾部斜杠
    console.log('测试 ping 端点...');
    const pingResponse = await axios.get('http://localhost:5888/api/ping');
    console.log('Ping成功:', pingResponse.data);
    
    // 添加尾部斜杠
    console.log('测试API根路径...');
    const rootResponse = await axios.get('http://localhost:5888/api/');
    console.log('API根路径连接成功:', rootResponse.data);
    
    // 添加尾部斜杠
    console.log('测试获取设备列表（无参数）...');
    try {
      const rawDevicesResponse = await axios.get('http://localhost:5888/api/devices/');
      console.log('获取设备列表成功（无参数）:', rawDevicesResponse.data);
    } catch (devError) {
      console.error('获取设备列表失败（无参数）:', devError.message);
      console.log('尝试明确指定参数...');
      
      // 添加尾部斜杠
      const paramsDevicesResponse = await axios.get('http://localhost:5888/api/devices/?skip=0&limit=10');
      console.log('获取设备列表成功（明确参数）:', paramsDevicesResponse.data);
    }
    
    ElMessage.success('API基本连接测试成功');
  } catch (error) {
    console.error('API基本连接测试失败:', error.message);
    
    if (error.response) {
      console.log('错误状态码:', error.response.status);
      console.log('响应内容:', error.response.data);
      
      if (error.response.data && error.response.data.detail) {
        console.log('错误详情:', error.response.data.detail);
        ElMessage.error(`API连接测试失败: ${error.response.status} - ${JSON.stringify(error.response.data.detail)}`);
      } else {
        ElMessage.error(`API连接测试失败: ${error.response.status} - ${error.response.statusText}`);
      }
    } else {
      ElMessage.error(`API连接测试失败: ${error.message}`);
    }
  }
  
  console.log('==== 基本API连接测试结束 ====');
};

const verifyConnection = async () => {
  console.log('开始验证API连接...');
  const axios = (await import('axios')).default;
  
  // 测试带尾部斜杠的路径
  try {
    const response = await axios.get('http://localhost:5888/api/devices/?skip=0&limit=1');
    console.log('带尾部斜杠的请求成功：', response.data);
    ElMessage.success('API连接成功！正确路径应使用尾部斜杠');
  } catch (error) {
    console.error('带尾部斜杠的请求失败：', error.message);
  }
  
  // 创建带斜杠的API实例测试
  try {
    const testApi = axios.create({
      baseURL: 'http://localhost:5888/api/devices/',
      timeout: 5000
    });
    const response = await testApi.get('/');
    console.log('使用带斜杠baseURL的API实例请求成功：', response.data);
  } catch (error) {
    console.error('使用带斜杠baseURL的API实例请求失败：', error.message);
  }
};

const simpleConnectionTest = async () => {
  const axios = (await import('axios')).default;
  
  console.log('开始连接测试...');
  
  try {
    // 测试系统 API
    console.log('测试系统级 API...');
    const systemResponse = await axios.get('http://localhost:5888/api/ping');
    console.log('系统 API 访问成功:', systemResponse.data);
    
    // 测试设备 API - 直接方式
    console.log('直接测试设备 API...');
    const devicesResponse = await axios.get('http://localhost:5888/api/devices/');
    console.log('设备 API 访问成功:', devicesResponse.data);
    
    // 测试 API 实例 - 确保封装正确
    console.log('使用 API 实例测试...');
    const apiResponse = await api.get('/');
    console.log('API 实例访问成功:', apiResponse.data);
    
    ElMessage.success('API 连接测试成功！');
    return true;
  } catch (error) {
    console.error('连接测试失败:', error);
    
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
    
    ElMessage.error(`API 连接测试失败: ${error.message}`);
    return false;
  }
};

// 创建一个系统 API 函数
const accessSystemApi = async (path) => {
  const axios = (await import('axios')).default;
  try {
    const url = `http://localhost:5888/api/${path}`;
    console.log(`访问系统 API: ${url}`);
    const response = await axios.get(url);
    console.log(`系统 API ${path} 访问成功:`, response.data);
    return response.data;
  } catch (error) {
    console.error(`系统 API ${path} 访问失败:`, error.message);
    throw error;
  }
};

const testSimpleData = async () => {
  try {
    console.log("开始简单数据测试...");
    const axios = (await import('axios')).default;
    
    // 尝试多种 URL 组合
    const urls = [
      'http://localhost:5888/api/devices/',
      'http://localhost:5888/api/devices',
      'http://localhost:5888/api/devices/?skip=0&limit=100'
    ];
    
    let successResponse = null;
    
    for (const url of urls) {
      try {
        console.log(`尝试请求: ${url}`);
        const response = await axios.get(url);
        console.log(`${url} 请求成功:`, response.data);
        
        if (Array.isArray(response.data) && response.data.length > 0) {
          successResponse = response;
          break;
        }
      } catch (err) {
        console.log(`${url} 请求失败:`, err.message);
      }
    }
    
    if (successResponse) {
      ElNotification({
        title: '获取成功',
        message: `成功获取 ${successResponse.data.length} 条设备数据!`,
        type: 'success',
        duration: 2000,
        position: 'bottom-right'
      });
      // 手动设置数据
      devices.value = successResponse.data;
      total.value = successResponse.data.length;
      return successResponse.data;
    } else {
      // 访问后端数据库检查接口
      try {
        const dbCheckResponse = await axios.get('http://localhost:5888/api/db-check');
        console.log("数据库检查结果:", dbCheckResponse.data);
        if (dbCheckResponse.data.status === "ok" && dbCheckResponse.data.device_count > 0) {
          ElMessage.warning(`数据库中有 ${dbCheckResponse.data.device_count} 台设备，但 API 未返回数据`);
        } else {
          ElMessage.warning("API 或数据库可能有问题，请检查后端日志");
        }
      } catch (dbErr) {
        console.error("数据库检查失败:", dbErr);
      }
    }
    
    return null;
  } catch (error) {
    console.error("简单数据测试失败:", error);
    ElMessage.error(`获取数据失败: ${error.message}`);
    return null;
  }
};

const fetchDevicesSimple = async () => {
  loading.value = true;
  try {
    const axios = (await import('axios')).default;
    const skip = (currentPage.value - 1) * pageSize.value;
    const limit = pageSize.value;
    
    // 使用相对路径让Vite代理处理请求
    const response = await axios.get(`/api/devices/?skip=${skip}&limit=${limit}`);
    
    if (Array.isArray(response.data)) {
      devices.value = response.data;
      total.value = response.data.length < limit ? skip + response.data.length : skip + limit + 1;
      ElNotification({
        title: '获取成功',
        message: `成功获取 ${response.data.length} 条数据`,
        type: 'success',
        duration: 3000,
        position: 'bottom-right'
      });
    } else {
      ElMessage.warning('API响应不是数组格式');
      console.log('非数组响应:', response.data);
    }
  } catch (error) {
    console.error('简化获取设备失败:', error);
    ElMessage.error(`获取设备失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

const checkHttpHeaders = async () => {
  try {
    const axios = (await import('axios')).default;
    const response = await axios.get(getApiUrl('devices/'), { 
      validateStatus: () => true 
    });
    
    console.log('HTTP 状态码:', response.status);
    console.log('响应头:', response.headers);
    console.log('Content-Type:', response.headers['content-type']);
    console.log('CORS 头:', {
      'Access-Control-Allow-Origin': response.headers['access-control-allow-origin'],
      'Access-Control-Allow-Methods': response.headers['access-control-allow-methods'],
      'Access-Control-Allow-Headers': response.headers['access-control-allow-headers']
    });
    
    return response;
  } catch (error) {
    console.error('检查 HTTP 头失败:', error);
    return null;
  }
};

const setTestData = () => {
  // 创建一些测试数据
  devices.value = [
    {
      id: 1,
      name: "测试路由器1",
      ip_address: "***********",
      device_type: "router",
      manufacturer: "cisco",
      model: "测试模型",
      is_active: true,
      username: "admin",
      password: "password",
      connection_type: "ssh",
      remote_type: "cisco_ios",
      port: 22,
      datacenter: "测试机房1",
      rack: "A01",
      position: 10
    },
    {
      id: 2,
      name: "测试交换机1",
      ip_address: "***********",
      device_type: "switch",
      manufacturer: "huawei",
      model: "S5700",
      is_active: false,
      username: "admin",
      password: "password",
      connection_type: "telnet",
      remote_type: "huawei_telnet",
      port: 23,
      datacenter: "测试机房2",
      rack: "B03",
      position: 15
    }
  ];
  
  total.value = devices.value.length;
  ElMessage.success("已设置测试数据，请检查表格渲染");
}

// 当API封装有问题时，可以使用这个函数进行直接调用
const callDirectAPI = async (method, path, data = null, params = null) => {
  try {
    const axios = (await import('axios')).default;
    const url = `/api/${path}`;
    console.log(`直接调用 ${method.toUpperCase()} ${url}`);
    
    const config = {
      params: params,
    };
    
    let response;
    if (method.toLowerCase() === 'get') {
      response = await axios.get(url, config);
    } else if (method.toLowerCase() === 'post') {
      response = await axios.post(url, data, config);
    } else if (method.toLowerCase() === 'put') {
      response = await axios.put(url, data, config);
    } else if (method.toLowerCase() === 'delete') {
      response = await axios.delete(url, config);
    }
    
    return response.data;
  } catch (error) {
    console.error(`API调用失败: ${method} ${path}`, error);
    throw error;
  }
};

// 添加一个存储所有设备的数组
const allDevices = ref([]);

// 添加API工具函数，统一处理API请求
const getApiUrl = (path) => {
  // 使用相对路径，让Vite代理配置处理
  return `/api/${path.startsWith('/') ? path.substring(1) : path}`;
};

// 获取所有设备然后在前端进行分页
const fetchAllDevicesAndPaginateLocally = async () => {
  try {
    loading.value = true;
    
    const axios = (await import('axios')).default;
    let allData = [];
    
    try {
      // 使用正确的API端点 "direct-devices" 而不是 "devices"
      //const response = await axios.get(getApiUrl('direct-devices'));
      const response = await axios.get(getApiUrl('devices/'));
      
      if (response.data && response.data.devices) {
        allData = response.data.devices;
        console.log(`成功获取 ${allData.length} 台设备`);
      } else if (Array.isArray(response.data)) {
        allData = response.data;
        console.log(`成功获取 ${allData.length} 台设备`);
      } else {
        console.warn('未预期的响应格式:', response.data);
        // 尝试在响应中找到一个数组
        for (const key in response.data) {
          if (Array.isArray(response.data[key]) && response.data[key].length > 0) {
            allData = response.data[key];
            console.log(`从响应中找到 ${allData.length} 台设备在键 ${key} 下`);
            break;
          }
        }
      }
    } catch (e) {
      console.error('获取所有设备失败:', e);
      ElMessage.error(`获取设备失败: ${e.message}`);
    }
    
    // 保存所有设备数据并应用本地分页
    allDevices.value = allData;
    total.value = allData.length;

    // 加载设备的IP关联信息
    await loadDeviceIPAssociations();

    applyLocalPagination();
    
  } catch (error) {
    console.error('获取和分页设备数据出错:', error);
    ElMessage.error(`获取设备列表失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

// 应用本地分页逻辑
const applyLocalPagination = () => {
  // 应用过滤逻辑
  let filteredDevices = allDevices.value;
  
  // 应用名称过滤
  if (filterForm.value.name) {
    filteredDevices = filteredDevices.filter(device => 
      matchDeviceName(device.name, filterForm.value.name)
    );
  }
  
  // 应用IP过滤
  if (filterForm.value.ip) {
    filteredDevices = filteredDevices.filter(device => 
      matchIpAddress(device.ip_address, filterForm.value.ip)
    );
  }
  
  // 应用设备类型过滤
  if (filterForm.value.type) {
    filteredDevices = filteredDevices.filter(device => 
      device.device_type === filterForm.value.type
    );
  }
  
  // 应用厂商过滤
  if (filterForm.value.manufacturer) {
    filteredDevices = filteredDevices.filter(device => 
      device.manufacturer === filterForm.value.manufacturer
    );
  }
  
  // 应用状态过滤
  if (filterForm.value.status) {
    const isActive = filterForm.value.status === 'active';
    filteredDevices = filteredDevices.filter(device => 
      device.is_active === isActive
    );
  }
  
  // 应用机房过滤
  if (filterForm.value.datacenter) {
    filteredDevices = filteredDevices.filter(device => 
      device.datacenter && device.datacenter.toLowerCase().includes(filterForm.value.datacenter.toLowerCase())
    );
  }
  
  // 应用机柜过滤
  if (filterForm.value.rack) {
    filteredDevices = filteredDevices.filter(device => 
      device.rack && device.rack.toLowerCase().includes(filterForm.value.rack.toLowerCase())
    );
  }
  
  // 如果有自定义排序，则应用自定义排序
  if (sortColumn.value && sortOrder.value) {
    filteredDevices.sort((a, b) => {
      // 处理序号列的特殊情况
      if (sortColumn.value === 'index') {
        // 序号列使用数组索引作为排序依据
        return sortOrder.value === 'ascending' ? 1 : -1;
      }
        
      let aValue = a[sortColumn.value];
      let bValue = b[sortColumn.value];
      
      // 处理数值型字段
      if (sortColumn.value === 'cpu_usage' || sortColumn.value === 'memory_usage' || 
          sortColumn.value === 'port' || sortColumn.value === 'position') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      }
      
      // 处理字符串字段
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        if (sortOrder.value === 'ascending') {
          return aValue.localeCompare(bValue);
        } else {
          return bValue.localeCompare(aValue);
        }
      }
      
      // 处理布尔或数值字段
      if (sortOrder.value === 'ascending') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });
  } else {
    // 按在线状态排序：在线设备优先显示
    filteredDevices.sort((a, b) => {
      // 首先按在线状态排序（在线的排在前面）
      if (a.is_active && !b.is_active) return -1;
      if (!a.is_active && b.is_active) return 1;
      
      // 如果在线状态相同，可以添加其他排序条件，例如按名称排序
      return a.name?.localeCompare(b.name || '') || 0;
    });
  }
  
  // 更新总记录数
  total.value = filteredDevices.length;
  
  // 计算分页
  const start = (currentPage.value - 1) * pageSize.value;
  const end = Math.min(start + pageSize.value, filteredDevices.length);
  
  // 显示当前页数据
  devices.value = filteredDevices.slice(start, end);
  console.log(`显示本地分页数据: ${start+1} 到 ${end}，共 ${filteredDevices.length} 条`);
};

// 确保过滤条件变化时进行本地分页
watch(() => filterForm.value, () => {
  if (allDevices.value.length > 0) {
    currentPage.value = 1; // 重置到第一页
    applyLocalPagination();
  }
}, { deep: true });

// 本地分页的页码变化处理
const handleLocalPageChange = (newPage) => {
  currentPage.value = newPage;
  applyLocalPagination();
};

// 本地分页的每页大小变化处理
const handleLocalSizeChange = (newSize) => {
  pageSize.value = newSize;
  currentPage.value = 1;
  applyLocalPagination();
};

// ==================== IP集成相关方法 ====================

// IP状态类型映射
const getIPStatusType = (status) => {
  const types = {
    'used': 'success',
    'available': 'info',
    'reserved': 'warning',
    'fault': 'danger'
  }
  return types[status] || 'info'
}

// IP状态文本映射
const getIPStatusText = (status) => {
  const texts = {
    'used': '已使用',
    'available': '可用',
    'reserved': '预留',
    'fault': '故障'
  }
  return texts[status] || status
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 关联IP地址
const associateIPAddress = async (device) => {
  try {
    const axios = (await import('axios')).default;
    // 这里可以打开一个对话框让用户选择IP地址，或者自动查找匹配的IP
    const response = await axios.post('/api/integration/associate', {
      device_id: device.id,
      ip_address: device.ip_address,
      is_primary: true
    })

    ElMessage.success('IP地址关联成功')

    // 刷新设备列表以显示新的关联关系
    await fetchAllDevicesAndPaginateLocally()

  } catch (error) {
    console.error('关联IP地址失败:', error)
    ElMessage.error(error.response?.data?.detail || '关联IP地址失败')
  }
}

// 加载设备的IP关联信息
const loadDeviceIPAssociations = async () => {
  try {
    const axios = (await import('axios')).default;
    const response = await axios.get('/api/integration/devices-with-ips', {
      params: {
        page: 1,
        page_size: 100  // 获取设备的IP信息（API限制最大100）
      }
    })

    const devicesWithIPs = response.data

    // 将IP信息合并到现有的设备数据中
    devices.value = devices.value.map(device => {
      const deviceWithIP = devicesWithIPs.find(item => item.device.id === device.id)
      if (deviceWithIP) {
        return {
          ...device,
          ip_info: {
            ...deviceWithIP.primary_ip,
            is_primary: true,
            subnet_name: deviceWithIP.primary_ip?.subnet?.name
          }
        }
      }
      return device
    })

  } catch (error) {
    console.error('加载IP关联信息失败:', error)
  }
}

// 注意：原有的fetchAllDevicesAndPaginateLocally函数已在上面定义，这里只需要在其中添加IP关联信息加载

// 组件挂载时调用 - 使用本地分页方法
onMounted(() => {
  fetchAllDevicesAndPaginateLocally();
});

// 暴露给模板的设置函数
const setAutoRefreshInterval = (seconds) => {
  autoRefreshInterval.value = seconds;
  if (refreshTimer) {
    clearInterval(refreshTimer);
    if (autoRefreshEnabled.value) {
      startAutoRefresh();
    }
  }
};

// 监听连接方式变化，更新端口
watch(() => deviceForm.value.connection_type, (newType, oldType) => {
  // 只有当连接方式发生变化时才考虑更新端口
  if (newType !== oldType) {
    // 检查当前端口是否是旧连接方式的默认端口
    const isOldDefaultPort = (oldType === 'ssh' && deviceForm.value.port === 22) ||
                            (oldType === 'telnet' && deviceForm.value.port === 23);

    // 在以下情况下自动更新端口：
    // 1. 添加模式
    // 2. 编辑模式下，端口未被手动修改过
    // 3. 编辑模式下，当前端口是旧连接方式的默认端口（用户可能期望自动切换）
    if (dialogType.value === 'add' ||
        (dialogType.value === 'edit' && (!deviceForm.value._portManuallySet || isOldDefaultPort))) {

      if (newType === 'ssh') {
        deviceForm.value.port = 22;
      } else if (newType === 'telnet') {
        deviceForm.value.port = 23;
      }

      // 在添加模式下重置端口手动修改标志
      if (dialogType.value === 'add') {
        deviceForm.value._portManuallySet = false;
      }

      console.log(`连接方式从 ${oldType} 变为 ${newType}，端口自动更新为 ${deviceForm.value.port}`);
    } else {
      console.log(`连接方式从 ${oldType} 变为 ${newType}，保持用户自定义端口: ${deviceForm.value.port}`);
    }
  }
});

// 监听端口变化，标记为手动修改
watch(() => deviceForm.value.port, (newPort, oldPort) => {
  // 只有当值确实发生了变化时才更新标记（避免初始化时设置）
  if (newPort !== oldPort && oldPort !== undefined) {
    // 检查新端口是否是当前连接方式的默认端口
    const isDefaultPort = (deviceForm.value.connection_type === 'ssh' && newPort === 22) ||
                         (deviceForm.value.connection_type === 'telnet' && newPort === 23);

    // 如果用户修改为非默认端口，标记为手动修改
    if (!isDefaultPort) {
      deviceForm.value._portManuallySet = true;
      console.log(`端口从 ${oldPort} 修改为 ${newPort}，标记为手动修改`);
    } else {
      // 如果用户修改为默认端口，可以重置手动修改标记
      deviceForm.value._portManuallySet = false;
      console.log(`端口从 ${oldPort} 修改为默认端口 ${newPort}，重置手动修改标记`);
    }
  }
});

// 获取远程类型 (用于Netmiko连接)
const getRemoteType = (manufacturer, connection_type) => {
  // 根据厂商和连接方式生成远程类型
  const manufacturerMap = {
    'huawei': 'huawei',
    'cisco': 'cisco_ios',
    'h3c': 'hp_comware',
    'ruijie': 'ruijie_os',
    'zte': 'zte_zxros'
  };
  
  let baseType = manufacturerMap[manufacturer] || 'cisco_ios';
  
  // 如果是telnet连接，添加_telnet后缀
  if (connection_type === 'telnet') {
    return `${baseType}_telnet`;
  }
  
  return baseType;
};

// 添加初始化为null的statusRefreshSource变量
let statusRefreshSource = null;

// 获取默认协议类型 - 简化为返回连接方式
const getDefaultProtocol = (manufacturer, connection_type = 'ssh') => {
  return connection_type;
};

// Add a watch for manufacturer changes to update protocol
watch(() => deviceForm.value.manufacturer, (newManufacturer) => {
  if (newManufacturer) {
    deviceForm.value.protocol = getDefaultProtocol(newManufacturer, deviceForm.value.connection_type);
  }
});

// 监听连接方式变化和厂商变化，自动更新protocol
watch([
  () => deviceForm.value.manufacturer,
  () => deviceForm.value.connection_type
], ([newManufacturer, newConnectionType], [oldManufacturer, oldConnectionType]) => {
  if (newManufacturer && newConnectionType) {
    // 设置protocol为连接方式
    deviceForm.value.protocol = newConnectionType;
    
    // 连接方式变化时的处理已经在单独的watch中完成，这里不再重复处理端口
    // 仅记录当前连接方式，以便其他地方使用
    deviceForm.value._previousConnectionType = newConnectionType;
    
    console.log(`厂商/连接方式更新: ${oldManufacturer}/${oldConnectionType} -> ${newManufacturer}/${newConnectionType}`);
  }
});

// 监听机房变化，重置机柜选择
watch(() => deviceForm.value.datacenter, (newDatacenter) => {
  // 如果机房变了，清空机柜选择
  if (newDatacenter) {
    deviceForm.value.rack = '';
    deviceForm.value.position = null;
  }
});

// 监听位置变化，验证是否已有设备
watch(
  () => [deviceForm.value.rack, deviceForm.value.position],
  async ([newRack, newPosition]) => {
    // 只有当机柜和位置都有值时才进行检查
    if (newRack && newPosition !== null) {
      // 添加短延迟避免频繁检查
      setTimeout(async () => {
        const isOccupied = await checkRackPositionOccupied(
          newRack,
          newPosition,
          dialogType.value === 'edit' ? deviceForm.value.id : null
        );
        
        if (isOccupied) {
          ElMessage.warning({
            message: '该机柜位置已被占用，请选择其他位置',
            duration: 3000
          });
        }
      }, 300);
    }
  }
);

// ... existing code ...
const getNetmikoDeviceType = (manufacturer, protocol) => {
  // Implement your logic to determine netmiko_device_type based on manufacturer and protocol
  // This is just a placeholder implementation
  return 'cisco_ios';
};

// 添加筛选重置函数
const resetFilter = () => {
  // 重置所有筛选表单值为空字符串
  filterForm.value = {
    name: '',
    ip: '',
    type: '',
    manufacturer: '',
    status: '',
    datacenter: '',
    rack: '',
    serial_number: ''
  };
  
  // 重置到第一页
  currentPage.value = 1;
  
  // 使用重置后的筛选条件获取设备
  fetchDevices();
  
  // 显示成功消息
  ElMessage.success('筛选条件已重置');
};

// 添加在其他响应式数据定义处
const columnDialogVisible = ref(false);
const visibleColumns = ref({
  index: true,
  manufacturer: true,
  device_type: true,
  name: true,
  model: true,
  ip_address: true,
  ip_usage: true,  // 新增：IP使用情况列
  protocol: true,
  port: true,
  status: true,
  datacenter: true,
  rack: true,
  position: true,
  cpu_usage: true,
  memory_usage: true,
  version_info: true,
  serial_number: true  // 确保序列号列可见
});

// 列选项配置
const columnOptions = [
  { key: 'index', label: '序号' },
  { key: 'manufacturer', label: '厂商' },
  { key: 'device_type', label: '设备类型' },
  { key: 'name', label: '设备名称' },
  { key: 'model', label: '型号' },
  { key: 'ip_address', label: 'IP地址' },
  { key: 'ip_usage', label: 'IP使用情况' },  // 新增：IP使用情况列
  { key: 'protocol', label: '连接方式' },
  { key: 'port', label: '端口' },
  { key: 'datacenter', label: '机房' },
  { key: 'rack', label: '机柜' },
  { key: 'position', label: 'U位' },
  { key: 'cpu_usage', label: 'CPU使用率' },
  { key: 'memory_usage', label: '内存使用率' },
  { key: 'version_info', label: '版本信息' },
  { key: 'status', label: '状态' },
  { key: 'serial_number', label: '序列号' }
];

// 打开列选择对话框 - 修正创建深拷贝的方式
const openColumnSelector = () => {
  // 创建临时副本，以便用户取消时不影响当前视图
  tempVisibleColumns.value = JSON.parse(JSON.stringify(visibleColumns.value));
  columnDialogVisible.value = true;
  
  // Initialize sortable after dialog is visible
  nextTick(() => {
    if (columnSortable.value) {
      // 如果已经有实例，先销毁
      if (sortableInstance) {
        sortableInstance.destroy();
      }
      
      // 创建新的Sortable实例，适配行列布局
      sortableInstance = Sortable.create(columnSortable.value.$el || columnSortable.value, {
        animation: 150,
        handle: '.drag-handle',
        ghostClass: 'column-ghost',
        draggable: '.column-option-col', // 改为拖拽列元素
        forceFallback: true,
        fallbackClass: 'column-ghost',
        fallbackOnBody: true,
        onEnd: (evt) => {
          // Update column order when dragging ends
          const itemEl = evt.item;
          const id = itemEl.querySelector('.column-option-item').getAttribute('data-id');
          
          console.log('拖拽结束:', {
            oldIndex: evt.oldIndex,
            newIndex: evt.newIndex,
            id: id
          });
          
          // 创建新的数组，避免直接修改原数组
          const newOrder = [...columnOrder.value];
          
          // 移除原位置的元素
          newOrder.splice(evt.oldIndex, 1);
          
          // 插入到新位置
          newOrder.splice(evt.newIndex, 0, id);
          
          // 更新列顺序
          columnOrder.value = newOrder;
          console.log('新的列顺序:', columnOrder.value);
        }
      });
      
      console.log('Sortable实例已创建');
    } else {
      console.warn('columnSortable元素未找到');
    }
  });
};

// 检查是否为最后一个选中的列
const isLastSelectedColumn = (key) => {
  // 计算当前选中的列数
  const selectedCount = Object.values(tempVisibleColumns.value).filter(Boolean).length;
  // 如果当前列被选中且只有一列被选中，则返回true
  return tempVisibleColumns.value[key] && selectedCount === 1;
};

// 保存列选择
const saveColumnSelection = () => {
  // 检查是否至少选择了一列
  const hasVisibleColumn = Object.values(tempVisibleColumns.value).some(v => v);
  if (!hasVisibleColumn) {
    ElMessage.warning('请至少选择一列显示');
    tempVisibleColumns.value.name = true; // 默认至少显示设备名称
    return; // 不关闭对话框，让用户继续选择
  }
  
  // 应用到实际显示
  visibleColumns.value = JSON.parse(JSON.stringify(tempVisibleColumns.value));
  
  // 保存到本地存储
  try {
    localStorage.setItem('deviceListColumns', JSON.stringify(visibleColumns.value));
    localStorage.setItem('deviceListColumnOrder', JSON.stringify(columnOrder.value));
    ElMessage.success('列显示设置已保存');
    console.log('保存的列顺序:', columnOrder.value);
  } catch (error) {
    console.error('保存列设置失败:', error);
  }
  
  columnDialogVisible.value = false;
  
  // Destroy sortable instance
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
};

// 取消列选择 - 不做任何改变，只关闭对话框
const cancelColumnSelection = () => {
  columnDialogVisible.value = false;
  
  // Destroy sortable instance
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
  }
  
  console.log('取消列选择，当前列顺序保持不变');
};

// 重置列显示为默认值
const resetColumnSelection = () => {
  // 重置为所有列都显示
  columnOptions.forEach(option => {
    tempVisibleColumns.value[option.key] = true;
  });
  
  // Reset column order to default
  columnOrder.value = columnOptions.map(option => option.key);
  
  // 如果有Sortable实例，需要销毁并重新创建
  if (sortableInstance) {
    sortableInstance.destroy();
    sortableInstance = null;
    
    // 重新初始化Sortable
    nextTick(() => {
      if (columnSortable.value) {
        sortableInstance = Sortable.create(columnSortable.value.$el || columnSortable.value, {
          animation: 150,
          handle: '.drag-handle',
          ghostClass: 'column-ghost',
          draggable: '.column-option-col', // 改为拖拽列元素，与openColumnSelector保持一致
          forceFallback: true,
          fallbackClass: 'column-ghost',
          fallbackOnBody: true,
          onEnd: (evt) => {
            const itemEl = evt.item;
            const id = itemEl.querySelector('.column-option-item').getAttribute('data-id');
            const newOrder = [...columnOrder.value];
            newOrder.splice(evt.oldIndex, 1);
            newOrder.splice(evt.newIndex, 0, id);
            columnOrder.value = newOrder;
          }
        });
      }
    });
  }
  
  console.log('已重置列顺序和显示状态');
};

// 临时存储列选择的变量
const tempVisibleColumns = ref({});

// 在组件挂载时加载用户保存的列设置
onMounted(() => {
  try {
    const savedColumns = localStorage.getItem('deviceListColumns');
    if (savedColumns) {
      const parsedColumns = JSON.parse(savedColumns);
      // 确保有至少一列可见
      if (Object.values(parsedColumns).some(v => v)) {
        visibleColumns.value = parsedColumns;
      }
    }
    
    // Load column order from localStorage
    const savedColumnOrder = localStorage.getItem('deviceListColumnOrder');
    if (savedColumnOrder) {
      try {
        const parsedOrder = JSON.parse(savedColumnOrder);
        // 验证解析出的数组是否有效
        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {
          // 确保所有的列都存在，如果有新增列或移除列，需要处理
          const validKeys = columnOptions.map(opt => opt.key);
          
          // 过滤出有效的列
          const validOrder = parsedOrder.filter(key => validKeys.includes(key));
          
          // 添加可能缺失的列（新增的列）
          validKeys.forEach(key => {
            if (!validOrder.includes(key)) {
              validOrder.push(key);
            }
          });
          
          // 更新列顺序
          columnOrder.value = validOrder;
          console.log('从本地存储加载的列顺序:', columnOrder.value);
        } else {
          console.warn('本地存储的列顺序无效，使用默认顺序');
        }
      } catch (parseError) {
        console.error('解析列顺序失败:', parseError);
      }
    }
    
    // 确保tempVisibleColumns有初始值
    tempVisibleColumns.value = JSON.parse(JSON.stringify(visibleColumns.value));
  } catch (error) {
    console.error('加载列设置失败:', error);
  }
  
  // 其余挂载代码...
});

// 添加计算属性，计算当前选择的列数
const countSelectedColumns = computed(() => {
  return Object.values(tempVisibleColumns.value).filter(Boolean).length;
});

// 添加选择列计算属性（用于复选框组）
const selectedColumns = computed({
  get() {
    return Object.entries(tempVisibleColumns.value)
      .filter(([_, isVisible]) => isVisible)
      .map(([key]) => key);
  },
  set(newValue) {
    // 更新逻辑由各个复选框的v-model直接处理
    console.log("Selected columns updated:", newValue);
  }
});

const deviceDetailVisible = ref(false)
const currentDevice = ref(null)
const showPassword = ref(false)

const togglePasswordVisibility = () => {
  showPassword.value = !showPassword.value;
}

const formatDate = (dateString) => {
  if (!dateString) return '-';
  try {
    const date = new Date(dateString);
    return date.toLocaleString();
  } catch (e) {
    return dateString;
  }
};

const maskPassword = (password) => {
  if (!password) return '-';
  return showPassword.value ? password : '••••••••';
};

const openDeviceConsole = () => {
  if (!currentDevice.value) return;
  
  ElMessage.info(`正在连接到设备控制台: ${currentDevice.value.name} (${currentDevice.value.ip_address})`);
  // 在这里可以添加打开控制台的实际代码
  // 例如，跳转到控制台页面或打开一个新的窗口
};

const connecting = ref(false);

// 使用开源WebSSH库
// import { Terminal } from 'xterm';
// import 'xterm/css/xterm.css';
// import { AttachAddon } from 'xterm-addon-attach';

// 在组件内定义必要的变量
const terminalVisible = ref(false);
const terminalDevice = ref(null);
// 终端打开方式偏好 - 'embed'(嵌入式) 或 'tab'(新标签页)
const terminalOpenPreference = ref(localStorage.getItem('terminalOpenPreference') || 'tab');

// 保存用户终端打开方式偏好
const saveTerminalPreference = (preference) => {
  terminalOpenPreference.value = preference;
  localStorage.setItem('terminalOpenPreference', preference);
  ElMessage.success(`已${preference === 'embed' ? '使用嵌入式终端' : '在新标签页打开终端'}`);
};

// 更新连接设备函数
const connectToDevice = async () => {
  if (!currentDevice.value || !currentDevice.value.is_active) {
    ElMessage.warning('只能连接在线的设备');
    return;
  }
  
  try {
    connecting.value = true;
    
    // 创建连接参数
    const connectionParams = {
      host: currentDevice.value.ip_address,
      port: currentDevice.value.port || (currentDevice.value.protocol === 'ssh' ? 22 : 23),
      username: currentDevice.value.username,
      password: currentDevice.value.password,
      protocol: currentDevice.value.protocol || 'ssh',
      device_id: currentDevice.value.id,
      device_type: currentDevice.value.netmiko_device_type || ''
    };
    
    // 调用后端API创建会话
    const axios = (await import('axios')).default;
    const response = await axios.post('/api/terminal/create-session', connectionParams);
    
    if (response.data && response.data.session_id) {
      // 成功创建会话，打开终端页面
      const sessionId = response.data.session_id;
      
      // 关闭详情对话框
      deviceDetailVisible.value = false;
      
      // 在新标签页中打开终端
      const terminalUrl = `/terminal?session=${sessionId}`;
      window.open(terminalUrl, '_blank');
      
      ElMessage.success('设备连接成功，正在打开终端...');
    } else {
      throw new Error('未获取到有效的会话ID');
    }
  } catch (error) {
    console.error('连接设备失败:', error);
    
    // 显示友好的错误信息
    let errorMsg = '连接设备失败';
    if (error.response) {
      if (error.response.status === 401) {
        errorMsg = '认证失败，请检查设备的用户名和密码';
      } else if (error.response.status === 404) {
        errorMsg = '无法连接到设备，请确认设备是否在线';
      } else if (error.response.data && error.response.data.detail) {
        errorMsg = `连接失败: ${error.response.data.detail}`;
      } else {
        errorMsg = `连接失败: 服务器返回错误 (${error.response.status})`;
      }
    } else if (error.message) {
      errorMsg = `连接失败: ${error.message}`;
    }
    
    ElMessage.error(errorMsg);
  } finally {
    connecting.value = false;
  }
};

// 关闭终端
const closeTerminal = () => {
  terminalVisible.value = false;
  terminalDevice.value = null;
};

const handleDeviceLogin = async (device) => {
  if (!device.is_active) {
    ElMessage.warning('只能连接在线的设备');
    return;
  }
  
  // 填充终端设备信息
  const deviceInfo = {
    ...device,
    // 确保端口和协议正确
    port: device.port || (device.protocol === 'ssh' ? 22 : 23),
    protocol: device.protocol || 'ssh'
  };
  
  // 始终使用标签页方式
  try {
    const axios = (await import('axios')).default;
    
    ElMessage.info(`正在创建到设备 ${deviceInfo.name} 的连接...`);
    
    // 创建会话
    const response = await axios.post('/api/terminal/create-session', {
      host: deviceInfo.ip_address,
      port: deviceInfo.port,
      username: deviceInfo.username,
      password: deviceInfo.password,
      protocol: deviceInfo.protocol,
      device_id: deviceInfo.id,
      device_type: deviceInfo.device_type || ''
    });
    
    if (response.data && response.data.session_id) {
      // 成功创建会话
      const sessionId = response.data.session_id;
      
      // 检查是否已有WebSSH窗口打开
      let websshWindow = null;
      
      // 先尝试获取已经存在的终端窗口引用
      websshWindow = window.open('', 'webssh_terminals');
      
      // 如果终端窗口已存在且已初始化
      if (websshWindow && !websshWindow.closed && websshWindow.location.href.includes('webssh')) {
        console.log('找到已存在的终端窗口，添加新会话');
        
        // 通过自定义事件向终端窗口传递新会话信息
        try {
          websshWindow.focus(); // 先激活窗口
          
          // 确保窗口已完全加载
          if(websshWindow.document.readyState === 'complete') {
            // 创建并分发添加终端事件
            const addTerminalEvent = new CustomEvent('addTerminal', {
              detail: {
                sessionId,
                deviceName: deviceInfo.name,
                deviceIp: deviceInfo.ip_address,
                vendor: deviceInfo.manufacturer // 添加厂商信息
              }
            });
            
            websshWindow.dispatchEvent(addTerminalEvent);
            console.log('已分发addTerminal事件');
            ElMessage.success(`设备 ${deviceInfo.name} 已添加到终端窗口`);
          } else {
            // 如果页面未完全加载，等待加载完成
            ElMessage.info('终端窗口正在加载中，请稍候...');
            
            // 使用URL参数传递会话信息，新开一个标签
            const terminalUrl = `/webssh?session=${sessionId}&name=${encodeURIComponent(deviceInfo.name)}&ip=${deviceInfo.ip_address}&vendor=${deviceInfo.manufacturer}`;
            window.open(terminalUrl, '_blank');
          }
        } catch (e) {
          console.error('向终端窗口传递事件失败:', e);
          // 如果发送事件失败，回退到打开新窗口方式
          const terminalUrl = `/webssh?session=${sessionId}&name=${encodeURIComponent(deviceInfo.name)}&ip=${deviceInfo.ip_address}&vendor=${deviceInfo.manufacturer}`;
          window.open(terminalUrl, '_blank');
        }
      } else {
        // 没有已打开的终端窗口或无法访问，创建新窗口
        console.log('创建新的终端窗口');
        const terminalUrl = `/webssh?session=${sessionId}&name=${encodeURIComponent(deviceInfo.name)}&ip=${deviceInfo.ip_address}`;
        window.open(terminalUrl, 'webssh_terminals');
        ElMessage.success(`设备 ${deviceInfo.name} 连接成功，正在打开终端窗口...`);
      }
    } else {
      throw new Error('创建会话失败，未获取到有效的会话ID');
    }
  } catch (error) {
    console.error('连接设备失败:', error);
    
    // 显示友好的错误信息
    let errorMsg = '连接设备失败';
    if (error.response) {
      if (error.response.status === 401) {
        errorMsg = '认证失败，请检查设备的用户名和密码';
      } else if (error.response.status === 404) {
        errorMsg = '无法连接到设备，请确认设备是否在线';
      } else if (error.response.data && error.response.data.detail) {
        errorMsg = `连接失败: ${error.response.data.detail}`;
      } else {
        errorMsg = `连接失败: 服务器返回错误 (${error.response.status})`;
      }
    } else if (error.message) {
      errorMsg = `连接失败: ${error.message}`;
    }
    
    ElMessage.error(errorMsg);
  }
};

const getSimpleData = async () => {
  try {
    loading.value = true;
    
    const axios = (await import('axios')).default;
    const skip = (currentPage.value - 1) * pageSize.value;
    const limit = pageSize.value;
    
    // 使用相对路径让Vite代理处理请求
    const response = await axios.get(getApiUrl(`devices/?skip=${skip}&limit=${limit}`));
    
    if (Array.isArray(response.data)) {
      devices.value = response.data;
      total.value = response.data.length < limit ? skip + response.data.length : skip + limit + 1;
      ElNotification({
        title: '获取成功',
        message: `成功获取 ${response.data.length} 条数据`,
        type: 'success',
        duration: 3000,
        position: 'bottom-right'
      });
    } else {
      ElMessage.warning('API响应不是数组格式');
      console.log('非数组响应:', response.data);
    }
  } catch (error) {
    console.error('简化获取设备失败:', error);
    ElMessage.error(`获取设备失败: ${error.message}`);
  } finally {
    loading.value = false;
  }
};

// 添加在其他方法附近，比如formatDate方法之后
const getCpuColor = (usage) => {
  const value = parseInt(usage) || 0;
  if (value < 30) return '#67C23A'; // 绿色 - 正常 (<30%)
  if (value < 50) return '#85CE61'; // 浅绿色 (30-50%)
  if (value < 70) return '#E6A23C'; // 橙色 (50-70%)
  if (value < 90) return '#F0C78A'; // 黄色 (70-90%)
  return '#F56C6C'; // 红色 - 危险 (>90%)
};

// 新增CPU文本颜色方法
const getCpuTextColor = (usage) => {
  const value = parseInt(usage) || 0;
  if (value < 30) return '#67C23A'; // 绿色 - 正常 (<30%)
  if (value < 50) return '#67C23A'; // 绿色 (30-50%)
  if (value < 70) return '#E6A23C'; // 橙色 (50-70%)
  if (value < 90) return '#E6A23C'; // 黄色 (70-90%)
  return '#F56C6C'; // 红色 - 危险 (>90%)
};

// 添加更新设备系统信息的方法
const updateDeviceSystemInfo = async () => {
  try {
    // 重置进度状态
    progressDialogVisible.value = true;
    progressDetails.value = [];
    progressSummary.value = "正在连接设备获取系统信息...";
    progressSuccess.value = false;
    progressPercent.value = 10;
    
    const axios = (await import('axios')).default;
    
    // 显示加载中状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在准备获取设备系统信息...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    
    // 获取在线设备数量（用于计算进度）
    progressPercent.value = 20;
    progressSummary.value = "正在检查在线设备...";
    
    const response = await axios.post('http://localhost:5888/api/devices/update-system-info');
    
    // 关闭通用加载状态
    loading.close();
    
    // 更新进度信息
    progressPercent.value = 100;
    progressSuccess.value = response.data.success;
    progressSummary.value = response.data.message;
    
    if (response.data.success) {
      // 设置成功消息
      ElMessage.success(response.data.message);
      // 显示详细信息
      if (response.data.details && response.data.details.length > 0) {
        progressDetails.value = response.data.details;
      }
      // 重新获取设备列表以展示更新后的信息
      await fetchDevices();
    } else {
      // 设置警告消息
      ElMessage.warning(response.data.message || '更新系统信息失败');
      // 显示详细信息
      if (response.data.details && response.data.details.length > 0) {
        progressDetails.value = response.data.details;
      }
    }
  } catch (error) {
    // 更新进度状态
    progressPercent.value = 100;
    progressSuccess.value = false;
    progressSummary.value = `更新设备系统信息失败: ${error.message}`;
    
    console.error('更新设备系统信息失败:', error);
    ElMessage.error(`更新设备系统信息失败: ${error.message}`);
  }
};

const getMemoryColor = (usage) => {
  const value = parseInt(usage) || 0;
  if (value < 30) return '#67C23A'; // 绿色 - 正常 (<30%)
  if (value < 50) return '#85CE61'; // 浅绿色 (30-50%)
  if (value < 70) return '#E6A23C'; // 橙色 (50-70%)
  if (value < 90) return '#F0C78A'; // 黄色 (70-90%)
  return '#F56C6C'; // 红色 - 危险 (>90%)
};

// 新增内存文本颜色方法
const getMemoryTextColor = (usage) => {
  const value = parseInt(usage) || 0;
  if (value < 30) return '#67C23A'; // 绿色 - 正常 (<30%)
  if (value < 50) return '#67C23A'; // 绿色 (30-50%)
  if (value < 70) return '#E6A23C'; // 橙色 (50-70%)
  if (value < 90) return '#E6A23C'; // 黄色 (70-90%)
  return '#F56C6C'; // 红色 - 危险 (>90%)
};

// 添加进度对话框相关变量
const progressDialogVisible = ref(false);
const progressDetails = ref([]);
const progressSummary = ref("");
const progressSuccess = ref(false);
const progressPercent = ref(0);

const navigateToDatacenterManagement = () => {
  router.push('/datacenter-management');
};

const navigateToRackManagement = () => {
  router.push('/rack-management');
};

const showAddDatacenterDialog = () => {
  addDatacenterDialogVisible.value = true;
};

// 获取机房列表
const fetchDatacenters = async () => {
  loadingDatacenters.value = true;
  try {
    const axios = (await import('axios')).default;
    const response = await axios.get('/api/datacenter/');
    
    if (response && response.data && Array.isArray(response.data)) {
      datacenters.value = response.data;
      console.log('获取到机房列表:', datacenters.value);
    } else {
      console.error('获取机房列表失败：数据格式错误');
      ElMessage.warning('获取机房列表失败，请手动刷新重试');
      datacenters.value = [];
    }
  } catch (error) {
    console.error('获取机房列表失败:', error);
    ElMessage.error(`获取机房列表失败: ${error.response?.data?.detail || error.message}`);
    datacenters.value = [];
  } finally {
    loadingDatacenters.value = false;
  }
};

// 获取机柜列表
const fetchRacks = async () => {
  loadingRacks.value = true;
  try {
    const axios = (await import('axios')).default;
    const response = await axios.get('/api/rack-management/');
    
    if (response && response.data && Array.isArray(response.data)) {
      racks.value = response.data;
      console.log('获取到机柜列表:', racks.value);
    } else {
      console.error('获取机柜列表失败：数据格式错误');
      ElMessage.warning('获取机柜列表失败，请手动刷新重试');
      racks.value = [];
    }
  } catch (error) {
    console.error('获取机柜列表失败:', error);
    ElMessage.error(`获取机柜列表失败: ${error.response?.data?.detail || error.message}`);
    racks.value = [];
  } finally {
    loadingRacks.value = false;
  }
};

// 根据选择的机房筛选机柜 (用于设备表单)
const filteredRacks = computed(() => {
  if (!deviceForm.value.datacenter) {
    return [];
  }
  
  // 查找选中的机房对象
  const selectedDatacenter = datacenters.value.find(dc => dc.name === deviceForm.value.datacenter);
  
  if (!selectedDatacenter) {
    return [];
  }
  
  // 根据机房ID筛选机柜
  return racks.value.filter(rack => rack.location === deviceForm.value.datacenter);
});

// 根据选择的机房筛选机柜 (用于筛选表单)
const filteredFilterRacks = computed(() => {
  if (!filterForm.value.datacenter) {
    return [];
  }
  
  // 根据机房筛选机柜
  return racks.value.filter(rack => rack.location === filterForm.value.datacenter);
});

// 检查当前选择的位置是否已被占用
const isCurrentPositionOccupied = computed(() => {
  if (!deviceForm.value.rack || deviceForm.value.position === null || occupiedPositions.value.length === 0) {
    return false;
  }
  
  // 编辑模式下，如果是当前设备占用的位置，则不算冲突
  if (dialogType.value === 'edit' && deviceForm.value.id) {
    // 获取当前位置的设备
    const currentPos = deviceForm.value.position;
    
    // 检查当前位置是否被占用但不是当前编辑的设备
    return occupiedPositions.value.includes(currentPos) && 
           !checkRackPositionIsCurrentDevice(deviceForm.value.rack, currentPos, deviceForm.value.id);
  }
  
  // 添加模式下，直接检查位置是否被占用
  return occupiedPositions.value.includes(deviceForm.value.position);
});

// 添加设备到机柜管理系统
const addDeviceToRack = async (deviceData, formData) => {
  try {
    console.log('添加设备到机柜管理系统:', deviceData);
    
    const axios = (await import('axios')).default;
    
    // 准备发送到机柜管理API的数据
    const rackDeviceData = {
      name: deviceData.name,
      type: mapDeviceTypeToRackType(deviceData.device_type),
      model: deviceData.model || '',
      position: parseInt(formData.position),
      u_size: 1, // 默认设备高度为1U
      power: 200, // 默认功率200W
      status: deviceData.is_active ? 'online' : 'offline',
      description: `从设备管理系统添加: ${deviceData.id}`,
      serial_number: deviceData.id.toString(), // 使用设备ID作为序列号
      ip_address: deviceData.ip_address
    };
    
    console.log('发送到机柜管理API的数据:', rackDeviceData);
    
    // 发送请求添加设备到机柜
    const response = await axios.post(
      `/api/rack-management/${formData.rack}/devices`,
      rackDeviceData
    );
    
    console.log('添加设备到机柜成功:', response.data);
    return response.data;
  } catch (error) {
    console.error('添加设备到机柜失败:', error);
    throw error;
  }
};

// 更新机柜管理系统中的设备
const updateDeviceInRack = async (deviceData, formData) => {
  try {
    console.log('更新机柜管理系统中的设备:', deviceData);
    
    const axios = (await import('axios')).default;
    
    // 首先尝试查找设备是否已存在于机柜中
    try {
      // 查询机柜中的设备
      const rackDevicesResponse = await axios.get(`/api/rack-management/${formData.rack}/devices`);
      const rackDevices = rackDevicesResponse.data || [];
      
      // 查找匹配的设备 (通过IP地址或名称匹配)
      const existingDevice = rackDevices.find(d => 
        (d.ip_address && d.ip_address === deviceData.ip_address) || 
        (d.name === deviceData.name && d.serial_number === deviceData.id.toString())
      );
      
      if (existingDevice) {
        // 如果设备已存在，则更新
        const updateData = {
          name: deviceData.name,
          type: mapDeviceTypeToRackType(deviceData.device_type),
          model: deviceData.model || existingDevice.model,
          position: parseInt(formData.position),
          status: deviceData.is_active ? 'online' : 'offline',
          ip_address: deviceData.ip_address
        };
        
        console.log('更新机柜中的设备:', updateData);
        
        // 发送更新请求
        const updateResponse = await axios.put(
          `/api/rack-management/${formData.rack}/devices/${existingDevice.id}`,
          updateData
        );
        
        console.log('更新机柜中的设备成功:', updateResponse.data);
        return updateResponse.data;
      } else {
        // 如果设备不存在，则添加
        return await addDeviceToRack(deviceData, formData);
      }
    } catch (error) {
      console.error('查询机柜设备失败:', error);
      // 如果查询失败，尝试直接添加设备
      return await addDeviceToRack(deviceData, formData);
    }
  } catch (error) {
    console.error('更新机柜中的设备失败:', error);
    throw error;
  }
};

// 从机柜管理系统中删除设备
const deleteDeviceFromRack = async (deviceData) => {
  try {
    console.log('从机柜管理系统中删除设备:', deviceData);
    
    // 如果设备没有机房、机柜或U位信息，则不需要从机柜管理系统中删除
    if (!deviceData.datacenter || !deviceData.rack) {
      console.log('设备没有机房或机柜信息，无需从机柜管理系统中删除');
      return;
    }
    
    const axios = (await import('axios')).default;
    
    // 首先尝试查找设备在机柜中的记录
    try {
      // 查询机柜中的设备
      const rackDevicesResponse = await axios.get(`/api/rack-management/${deviceData.rack}/devices`);
      const rackDevices = rackDevicesResponse.data || [];
      
      // 查找匹配的设备 (通过IP地址、名称或序列号匹配)
      const existingDevice = rackDevices.find(d => 
        (d.ip_address && d.ip_address === deviceData.ip_address) || 
        (d.name === deviceData.name) ||
        (d.serial_number === deviceData.id.toString())
      );
      
      if (existingDevice) {
        // 如果设备存在，则删除
        console.log('找到机柜中的设备记录，准备删除:', existingDevice);
        
        // 发送删除请求
        const deleteResponse = await axios.delete(
          `/api/rack-management/${deviceData.rack}/devices/${existingDevice.id}`
        );
        
        console.log('从机柜中删除设备成功:', deleteResponse.data);
        return deleteResponse.data;
      } else {
        console.log('未在机柜中找到对应设备记录，无需删除');
        return null;
      }
    } catch (error) {
      console.error('查询或删除机柜设备失败:', error);
      throw error;
    }
  } catch (error) {
    console.error('从机柜管理系统中删除设备失败:', error);
    throw error;
  }
};

// 将设备类型映射为机柜管理系统中的设备类型
const mapDeviceTypeToRackType = (deviceType) => {
  const typeMap = {
    'router': 'network',
    'switch': 'network',
    'firewall': 'security',
    'loadbalancer': 'network',
    'server': 'server',
    'desktop': 'computer',
    'printer': 'peripheral',
    'laptop': 'computer',
    'ac_controller': 'network',
    'access_point': 'network'
  };

  return typeMap[deviceType] || 'network';
};

// 处理添加机房
const handleAddDatacenter = async (newDatacenter) => {
  try {
    // 发送表单数据到后端API
    console.log("发送到后端的数据：", newDatacenter);
    
    const datacenterData = {
      name: newDatacenter.name,
      location: newDatacenter.location || "",
      description: newDatacenter.description || ""
    };
    
    console.log("处理后准备发送的数据:", datacenterData);
    
    // 使用正确格式的URL发送请求
    const datacenterApiUrl = '/api/datacenter/';
    const axios = (await import('axios')).default;
    
    const response = await axios.post(datacenterApiUrl, datacenterData);
    if (response.data) {
      console.log("添加机房成功，服务器返回:", response.data);
      ElMessage.success(`机房 ${newDatacenter.name} 添加成功`);
      
      // 刷新机房和机柜列表
      await refreshDatacentersAndRacks();
      
      // 刷新可能需要使用datacenter字段过滤的设备
      if (filterForm.value.datacenter) {
        await fetchDevices();
      }
    }
  } catch (error) {
    console.error("添加机房失败:", error);
    ElMessage.error(`添加机房失败: ${error.response?.data?.detail || error.message}`);
  }
};

// 添加columnOrder ref变量，确保列顺序正确
const columnOrder = ref([
  'index',
  'manufacturer',
  'device_type',
  'name',
  'serial_number', // 将序列号列提前，使其在名称之后立即显示
  'model',
  'ip_address',
  'protocol',
  'port',
  'status',
  'datacenter',
  'rack',
  'position',
  'cpu_usage',
  'memory_usage',
  'version_info'
]);

// Add columnSortable ref and modify the openColumnSelector function
const columnSortable = ref(null);
let sortableInstance = null;

// 检查位置是否是当前编辑的设备
const checkRackPositionIsCurrentDevice = (rackId, position, deviceId) => {
  // 这是一个同步函数，用于检查占用的位置是否是当前设备
  // 这里简化处理，实际中可能需要异步获取机柜设备信息
  if (!deviceId) return false;
  
  // 假设当前位置如果在已占用列表中，但deviceForm中有该位置，则认为是当前设备
  return deviceForm.value && 
         deviceForm.value.id === deviceId && 
         deviceForm.value.position === position;
};

// 检查机柜位置是否已被占用
const checkRackPositionOccupied = async (rackId, position, currentDeviceId = null) => {
  try {
    const axios = (await import('axios')).default;
    
    // 获取机柜中的所有设备
    const response = await axios.get(`/api/rack-management/${rackId}/devices`);
    const rackDevices = response.data || [];
    
    // 查找是否有设备占用了这个位置
    const deviceAtPosition = rackDevices.find(device => 
      device.position === position || 
      (device.position <= position && position < device.position + (device.u_size || 1))
    );
    
    // 如果是编辑现有设备，且找到的是同一个设备，则不算冲突
    if (deviceAtPosition && currentDeviceId) {
      // 检查是否为当前正在编辑的设备（通过查找关联的设备管理系统ID）
      const isCurrentDevice = deviceAtPosition.description?.includes(currentDeviceId.toString()) ||
                             deviceAtPosition.serial_number === currentDeviceId.toString();
      
      if (isCurrentDevice) {
        return false; // 不算冲突
      }
    }
    
    // 返回是否有设备占用此位置
    return !!deviceAtPosition;
  } catch (error) {
    console.error('检查机柜位置占用状态失败:', error);
    // 发生错误时，为安全起见返回false，允许继续提交
    return false;
  }
};

const occupiedPositions = ref([]);

// 获取机柜内已占用的位置
const fetchOccupiedPositions = async (rackId) => {
  if (!rackId) {
    occupiedPositions.value = [];
    return;
  }
  
  try {
    const axios = (await import('axios')).default;
    const response = await axios.get(`/api/rack-management/${rackId}/devices`);
    const rackDevices = response.data || [];
    
    // 收集所有已占用的位置（考虑设备高度）
    const occupiedPos = [];
    rackDevices.forEach(device => {
      const position = device.position;
      const uSize = device.u_size || 1;
      
      // 将设备占用的每个U位都添加到列表中
      for (let i = 0; i < uSize; i++) {
        occupiedPos.push(position + i);
      }
    });
    
    // 排序后更新状态
    occupiedPositions.value = [...new Set(occupiedPos)].sort((a, b) => a - b);
    console.log('已占用的位置:', occupiedPositions.value);
    
  } catch (error) {
    console.error('获取机柜已占用位置失败:', error);
    ElMessage.warning('无法获取机柜已占用位置信息');
    occupiedPositions.value = [];
  }
};

const handleRackChange = async (value) => {
  // 清空位置选择
  deviceForm.value.position = null;
  
  // 获取机柜已占用位置
  await fetchOccupiedPositions(value);
};

const checkPositionAvailability = async (value) => {
  if (!deviceForm.value.rack || value === null) return;
  
  // 检查选择的位置是否已被占用
  const isOccupied = await checkRackPositionOccupied(
    deviceForm.value.rack,
    value,
    dialogType.value === 'edit' ? deviceForm.value.id : null
  );
  
  if (isOccupied) {
    ElMessage.warning({
      message: '该机柜位置已被占用，请选择其他位置',
      duration: 3000
    });
  }
};

// 刷新机房和机柜数据
const refreshDatacentersAndRacks = async () => {
  try {
    await Promise.all([
      fetchDatacenters(),
      fetchRacks()
    ]);
    console.log('已刷新机房和机柜数据');
  } catch (error) {
    console.error('刷新机房和机柜数据失败:', error);
    ElMessage.warning('刷新机房和机柜数据失败，请手动刷新页面');
  }
};

// IP冲突检测方法
const detectIpConflicts = async () => {
  try {
    ipConflictLoading.value = true;

    const axios = (await import('axios')).default;
    const response = await axios.get('http://localhost:5888/api/devices/ip-conflicts');

    if (response.data.success) {
      ipConflictData.value = response.data;
      ipConflictDialogVisible.value = true;

      if (response.data.total_conflicts > 0) {
        ElMessage.warning(`发现 ${response.data.total_conflicts} 个IP冲突`);
      } else {
        ElMessage.success('未发现IP冲突');
      }
    } else {
      ElMessage.error('IP冲突检测失败');
    }
  } catch (error) {
    console.error('IP冲突检测失败:', error);
    ElMessage.error(`IP冲突检测失败: ${error.message}`);
  } finally {
    ipConflictLoading.value = false;
  }
};

// 自定义设备类型和厂商功能
const openCustomTypeDialog = () => {
  customTypeForm.value = { key: '', label: '', isEditing: false, originalKey: '' };
  activeCustomTypeTab.value = 'add';
  customTypeDialogVisible.value = true;
};

const openCustomManufacturerDialog = () => {
  customManufacturerForm.value = { key: '', label: '', isEditing: false, originalKey: '' };
  activeCustomManufacturerTab.value = 'add';
  customManufacturerDialogVisible.value = true;
};

const addCustomDeviceType = () => {
  const { key, label, isEditing, originalKey } = customTypeForm.value;

  if (!key || !label) {
    ElMessage.warning('请填写完整的设备类型信息');
    return;
  }

  if (isEditing) {
    // 编辑模式
    if (key !== originalKey && deviceTypeOptions.value[key]) {
      ElMessage.warning('该设备类型标识已存在');
      return;
    }

    // 删除原有的键（如果键发生了变化）
    if (key !== originalKey) {
      delete deviceTypeOptions.value[originalKey];
    }

    // 更新设备类型
    deviceTypeOptions.value[key] = label;
    ElMessage.success(`成功更新设备类型：${label}`);
  } else {
    // 添加模式
    if (deviceTypeOptions.value[key]) {
      ElMessage.warning('该设备类型标识已存在');
      return;
    }

    // 添加到选项中
    deviceTypeOptions.value[key] = label;

    // 自动选择新添加的类型
    deviceForm.value.device_type = key;

    ElMessage.success(`成功添加设备类型：${label}`);
  }

  // 保存到本地存储
  saveCustomOptions();

  // 重置表单并切换到管理标签页
  customTypeForm.value = { key: '', label: '', isEditing: false, originalKey: '' };
  activeCustomTypeTab.value = 'manage';
};

const addCustomManufacturer = () => {
  const { key, label, isEditing, originalKey } = customManufacturerForm.value;

  if (!key || !label) {
    ElMessage.warning('请填写完整的厂商信息');
    return;
  }

  if (isEditing) {
    // 编辑模式
    if (key !== originalKey && manufacturerOptions.value[key]) {
      ElMessage.warning('该厂商标识已存在');
      return;
    }

    // 删除原有的键（如果键发生了变化）
    if (key !== originalKey) {
      delete manufacturerOptions.value[originalKey];
    }

    // 更新厂商
    manufacturerOptions.value[key] = label;
    ElMessage.success(`成功更新厂商：${label}`);
  } else {
    // 添加模式
    if (manufacturerOptions.value[key]) {
      ElMessage.warning('该厂商标识已存在');
      return;
    }

    // 添加到选项中
    manufacturerOptions.value[key] = label;

    // 自动选择新添加的厂商
    deviceForm.value.manufacturer = key;

    ElMessage.success(`成功添加厂商：${label}`);
  }

  // 保存到本地存储
  saveCustomOptions();

  // 重置表单并切换到管理标签页
  customManufacturerForm.value = { key: '', label: '', isEditing: false, originalKey: '' };
  activeCustomManufacturerTab.value = 'manage';
};

// 保存自定义选项到本地存储
const saveCustomOptions = () => {
  try {
    localStorage.setItem('customDeviceTypes', JSON.stringify(deviceTypeOptions.value));
    localStorage.setItem('customManufacturers', JSON.stringify(manufacturerOptions.value));
  } catch (error) {
    console.warn('保存自定义选项失败:', error);
  }
};

// 从本地存储加载自定义选项
const loadCustomOptions = () => {
  try {
    const savedDeviceTypes = localStorage.getItem('customDeviceTypes');
    const savedManufacturers = localStorage.getItem('customManufacturers');

    if (savedDeviceTypes) {
      const customTypes = JSON.parse(savedDeviceTypes);
      deviceTypeOptions.value = { ...deviceTypeOptions.value, ...customTypes };
    }

    if (savedManufacturers) {
      const customManufacturers = JSON.parse(savedManufacturers);
      manufacturerOptions.value = { ...manufacturerOptions.value, ...customManufacturers };
    }
  } catch (error) {
    console.warn('加载自定义选项失败:', error);
  }
};

// 获取自定义设备类型列表（排除预定义的）
const getCustomDeviceTypes = () => {
  const predefinedTypes = ['router', 'switch', 'firewall', 'loadbalancer', 'desktop', 'printer', 'laptop', 'ac_controller', 'access_point', 'server'];
  return Object.entries(deviceTypeOptions.value)
    .filter(([key]) => !predefinedTypes.includes(key))
    .map(([key, label]) => ({ key, label }));
};

// 获取自定义厂商列表（排除预定义的）
const getCustomManufacturers = () => {
  const predefinedManufacturers = ['huawei', 'cisco', 'h3c', 'ruijie', 'zte', 'nsfocus', 'topsec', 'juniper', 'maipu', 'fiberhome', 'bell', 'digitalchina', 'sangfor', 'hillstone', 'venustech', 'dptech', 'fortinet', 'paloalto', 'checkpoint', 'f5', 'citrix', 'radware', 'a10', 'array', 'other'];
  return Object.entries(manufacturerOptions.value)
    .filter(([key]) => !predefinedManufacturers.includes(key))
    .map(([key, label]) => ({ key, label }));
};

// 编辑自定义设备类型
const editCustomDeviceType = (item) => {
  customTypeForm.value = {
    key: item.key,
    label: item.label,
    isEditing: true,
    originalKey: item.key
  };
  activeCustomTypeTab.value = 'add';
};

// 编辑自定义厂商
const editCustomManufacturer = (item) => {
  customManufacturerForm.value = {
    key: item.key,
    label: item.label,
    isEditing: true,
    originalKey: item.key
  };
  activeCustomManufacturerTab.value = 'add';
};

// 删除自定义设备类型
const deleteCustomDeviceType = async (key) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设备类型"${deviceTypeOptions.value[key]}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    delete deviceTypeOptions.value[key];
    saveCustomOptions();
    ElMessage.success('设备类型删除成功');
  } catch {
    // 用户取消删除
  }
};

// 删除自定义厂商
const deleteCustomManufacturer = async (key) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除厂商"${manufacturerOptions.value[key]}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    delete manufacturerOptions.value[key];
    saveCustomOptions();
    ElMessage.success('厂商删除成功');
  } catch {
    // 用户取消删除
  }
};
</script>

<style scoped>
.page-container {
  padding: 24px 0;
  max-width: 100%;
  width: 100%;
  margin: 0;
  background-color: #f8f9fc;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px; /* 增加底部间距从20px到32px */
}

/* Form layout styles */
.two-column-form .el-row {
  margin-bottom: 15px;
}

.two-column-form .el-form-item {
  margin-bottom: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
}

.position-input-container {
  display: flex;
  align-items: center;
}

.position-info-icon {
  margin-left: 8px;
  color: #909399;
  cursor: help;
}

.position-warning {
  color: #F56C6C;
  font-size: 12px;
  margin-top: 4px;
}

.position-input-danger {
  border-color: #F56C6C !important;
}

/* CPU和内存使用率的样式 */
.usage-container {
  display: flex;
  align-items: center;
  gap: 0;
  width: 100%;
}

.progress-wrapper {
  width: 120px;
  flex-shrink: 0;
}

.short-progress {
  width: 100%;
}

.usage-value {
  font-weight: 500;
  min-width: 30px;
  text-align: left;
  white-space: nowrap;
  margin-left: 1px;
}

.usage-value-only {
  font-weight: 500;
  font-size: 15px;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.page-actions {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  padding-bottom: 6px;
  margin-top: 12px; /* 减少顶部间距从16px到12px，保持与标签页导航的适当分离 */
}

/* Button classes for consistent styling */
.btn-primary, .btn-warning, .btn-success, .btn-danger {
  border: none !important;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  font-size: 13px;
}

.btn-primary {
  background-color: #409EFF !important;
  color: white !important;
}

.btn-warning {
  background-color: #F56C6C !important;
  color: white !important;
}

.btn-success {
  background-color: #67C23A !important;
  color: white !important;
}

.btn-danger {
  background-color: #F56C6C !important;
  color: white !important;
}

/* Modern button styling */
.page-actions .el-button {
  border-radius: 4px;
  transition: all 0.2s ease;
  box-shadow: none;
  color: white;
  font-weight: 500;
  height: 34px;
  padding: 0 14px;
}

.page-actions .el-button:hover {
  opacity: 0.85;
  transform: translateY(-1px);
}

/* Card styling with subtle shadow and rounded corners */
.combined-card {
  margin-bottom: 16px; /* 减少底部间距从24px到16px */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  overflow: hidden;
  background-color: white;
  border: none;
  width: 100%;
}

/* Filter area styling */
.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px; /* 减少底部间距从16px到8px */
  flex-wrap: wrap;
  gap: 8px;
  background-color: #ffffff;
  padding: 12px 16px; /* 减少上下内边距从16px到12px */
  border-radius: 6px;
  border: none;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.02);
  width: 100%;
}

/* Filter fields area */
.filter-fields {
  flex: 1;
  overflow-x: auto;
  padding-bottom: 5px;
  white-space: nowrap;
}

.compact-input {
  width: 140px !important;
}

/* 不同宽度的输入框类 */
.input-small {
  width: 145px !important;
}

.input-medium {
  width: 190px !important;
}

.input-large {
  width: 180px !important;
}

.input-xlarge {
  width: 220px !important;
}

.compact-select {
  width: 120px !important;
}

/* 不同宽度的选择框类 */
.select-small {
  width: 80px !important;
}

.select-medium {
  width: 100px !important;
}

.select-large {
  width: 115px !important;
}

.select-xlarge {
  width: 200px !important;
}

.filter-select {
  width: 100px !important;
}

/* Form styling */
.device-filter-form {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  gap: 8px;
  overflow-x: auto;
  white-space: nowrap;
  padding-bottom: 5px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.compact-form-item {
  margin-right: 0 !important;
  margin-bottom: 0 !important;
  flex-shrink: 0;
}

/* Filter buttons */
.filter-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  flex-shrink: 0;
}

.filter-btn {
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 6px 12px;
  height: 32px;
}

:deep(.filter-btn .el-icon) {
  margin-right: 4px;
}

/* Saved filters */
.saved-filters {
  margin: 6px 0; /* 减少上下间距从12px到6px */
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.saved-filter-tag {
  cursor: pointer;
  transition: all 0.2s;
}

.saved-filter-tag:hover {
  transform: translateY(-1px);
}

/* Sorting info alert */
.sorting-info {
  margin: 12px 0;
  position: relative;
}

.sorting-info :deep(.el-alert) {
  border-radius: 4px;
  background-color: #f8faff;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.02);
  padding: 8px 16px; /* 减少上下内边距从10px到8px */
  margin-bottom: 8px; /* 减少底部间距从16px到8px */
}

.sorting-info :deep(.el-alert .el-alert__content) {
  width: 100%;
}

.sorting-info :deep(.el-alert__title) {
  font-size: 13px;
  width: 100%;
  color: #606266;
  font-weight: 400;
}

.sorting-info :deep(.el-alert__icon) {
  color: #909399;
}

.sorting-info-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
  flex-wrap: nowrap;
}

.sorting-info-container > span {
  flex: 1;
  white-space: normal;
}

.reset-sort-btn {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 4px 10px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: auto;
  transition: all 0.2s;
  white-space: nowrap;
  flex-shrink: 0;
  height: 26px;
  line-height: 1;
  font-weight: 400;
}

.reset-sort-btn:hover {
  background-color: #40a9ff;
  opacity: 0.9;
}

/* Table styling */
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: none;
  border: 1px solid #ebeef5;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #303133;
  font-weight: 500;
  font-size: 13px;
  height: 46px;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

/* 确保表头文字在一行显示并居中 */
:deep(.el-table th .cell) {
  white-space: nowrap !important;
  overflow: visible;
  text-overflow: clip;
  word-break: keep-all;
  line-height: 22px;
  width: 100%;
  position: relative;
  text-align: center !important;
}

/* 修复表头排序图标位置 */
:deep(.el-table th.is-sortable .cell)::after {
  right: 0;
}

/* 添加表头单元格类名 */
.table-header-cell {
  text-align: center !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-table__header) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table td) {
  padding: 12px 0;
  height: 54px;
}

:deep(.device-table-row) {
  transition: background-color 0.2s ease;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafbfc;
}

/* Progress bar styling */
:deep(.el-progress-bar__outer) {
  border-radius: 4px;
  background-color: #f0f0f0;
  height: 8px !important;
}

:deep(.el-progress-bar__inner) {
  border-radius: 4px;
  transition: width 0.6s ease;
}

:deep(.el-progress--line) {
  margin: 0;
  width: 100%;
}

/* Status tag styling */
.status-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 0 8px;
  height: 26px;
  border-radius: 4px;
  font-size: 12px;
}

:deep(.el-tag) {
  border-radius: 4px;
  font-size: 12px;
}

/* Action buttons - 保持原有样式兼容性 */
.action-buttons {
  display: flex;
  justify-content: center;
  gap: 8px;
}

:deep(.action-buttons .el-button.is-circle) {
  padding: 7px;
  width: 34px;
  height: 34px;
  margin: 0 2px;
}

:deep(.action-buttons .el-button.is-circle .el-icon) {
  font-size: 16px;
}

/* 新的行内操作按钮样式 */
:deep(.operation-buttons-inline .el-button.is-circle) {
  padding: 6px;
  width: 30px;
  height: 30px;
  margin: 0;
}

:deep(.operation-buttons-inline .el-button.is-circle .el-icon) {
  font-size: 14px;
}

/* Pagination styling */
:deep(.el-pagination) {
  margin-top: 20px;
  justify-content: flex-end;
  padding: 0;
}

:deep(.el-pagination .el-pagination__sizes .el-input .el-input__inner) {
  font-size: 13px;
}

:deep(.el-pagination .btn-prev, 
.el-pagination .btn-next,
.el-pagination .el-pager li) {
  background: transparent;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  margin: 0 2px;
}

:deep(.el-pagination .el-pager li.active) {
  background-color: #409eff;
  color: #fff;
  border-color: #409eff;
}

/* Table footer */
.table-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0 4px;
}

.batch-action-btn {
  padding: 8px 16px;
  font-weight: 500;
  border: none;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .filter-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-buttons {
    margin-left: 0;
    margin-top: 8px;
    width: 100%;
    justify-content: flex-start;
  }
  
  .table-footer {
    flex-direction: column;
    align-items: flex-start;
  }
  
  :deep(.el-pagination) {
    width: 100%;
    justify-content: center;
  }
}

/* Status indicator styling */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 10px;
}

.status-online {
  color: #67C23A; /* 绿色 - 在线 */
  background-color: rgba(103, 194, 58, 0.1);
}

.status-offline {
  color: #909399; /* 灰色 - 离线 */
  background-color: rgba(144, 147, 153, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  display: inline-block;
}

.status-online .status-dot {
  background-color: #67C23A;
}

.status-offline .status-dot {
  background-color: #909399;
}

/* Connection type styling */
.connection-type {
  background-color: #f0f7ff;
  padding: 3px 8px;
  border-radius: 4px;
  color: #409eff;
  font-weight: 500;
}

/* 自动登录确认对话框样式 */
.auto-login-confirm .el-message-box__content {
  line-height: 1.6;
  white-space: pre-line;
}

.auto-login-confirm .el-message-box__message {
  font-size: 14px;
  color: #606266;
}

.auto-login-confirm .el-message-box__title {
  color: #409eff;
  font-weight: 600;
  font-size: 12px;
  display: inline-block;
  min-width: 50px;
}

/* 自选显示列对话框样式 */
.column-selection-tip {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.column-options-row {
  margin: 0;
  padding: 8px 0;
}

.column-option-col {
  margin-bottom: 12px;
}

.column-option-item {
  border: 1px solid #ebeef5;
  padding: 8px 10px;
  border-radius: 4px;
  background-color: #fafafa;
  display: flex;
  justify-content: space-between;
  transition: all 0.2s;
  cursor: grab;
}

.column-option-item:hover {
  background-color: #f0f7ff;
  border-color: #d0e6ff;
}

.column-option-content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-checkbox {
  font-size: 13px;
}

.drag-handle {
  cursor: move;
  display: flex;
  align-items: center;
  gap: 4px;
  color: #909399;
  margin-left: 8px;
  padding: 3px 6px;
  border: 1px dashed #c0c4cc;
  border-radius: 3px;
  background-color: #f5f7fa;
  transition: all 0.2s;
}

.drag-handle:hover {
  background-color: #ebeef5;
  color: #606266;
}

.drag-text {
  font-size: 12px;
  font-weight: 400;
}

/* 自定义弹窗样式 */
:deep(.el-dialog) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background-color: #f8f9fc;
  padding: 16px 20px;
  margin: 0;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__title) {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #ebeef5;
  background-color: #f8f9fc;
}

.full-width {
  width: 100%;
  max-width: 100%;
  margin: 0;
  padding: 0 16px;
  box-sizing: border-box;
}

/* IP冲突检测对话框样式 */
.ip-conflict-content {
  min-height: 200px;
}

.conflict-summary {
  display: flex;
  gap: 20px;
  margin: 0;
}

.conflict-summary p {
  margin: 0;
  font-size: 14px;
  color: #606266;
}

.conflict-group {
  margin-bottom: 20px;
}

.conflict-card {
  border: 1px solid #f56c6c;
  border-radius: 6px;
}

.conflict-card :deep(.el-card__header) {
  background-color: #fef0f0;
  border-bottom: 1px solid #f56c6c;
}

.conflict-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conflict-ip {
  font-weight: 600;
  color: #f56c6c;
  font-size: 16px;
}

.no-conflicts {
  text-align: center;
  padding: 40px 0;
}

.mb-4 {
  margin-bottom: 16px;
}

/* IP使用情况列样式优化 */
.ip-usage-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.ip-status-tags {
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.ip-subnet-info {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
}

.ip-no-info {
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

/* 操作按钮行内布局 */
.operation-buttons-inline {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  white-space: nowrap;
}

.operation-buttons-inline .el-button {
  margin: 0;
}

/* 表格单元格内容优化 */
:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 0 8px;
}

/* 确保表格行高一致 */
:deep(.el-table__row) {
  height: 48px;
}

:deep(.el-table__row .cell) {
  line-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>